#!/usr/bin/env python3
"""
测试直接Ollama调用的脚本
"""

import asyncio
import sys
import os
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Utils.logs.LoggingConfig import logger

# 测试用的简历内容
SAMPLE_RESUME = """
姓名：张三
性别：男
年龄：28
学历：本科
邮箱：<EMAIL>
手机：13800138000
现居地址：北京市朝阳区
政治面貌：群众

求职意向：
期望职位：Python开发工程师
期望薪资：15-25K
期望城市：北京

教育经历：
2016-2020 北京理工大学 计算机科学与技术 本科

工作经历：
2020.07-2023.05 ABC科技有限公司 Python开发工程师
主要负责后端API开发，使用Django框架开发Web应用

项目经验：
1. 电商平台后端系统
   时间：2021.03-2022.08
   职责：负责订单模块和支付模块的开发
   技术栈：Python, Django, MySQL, Redis

技能：
- 熟练掌握Python、Django、Flask
- 熟悉MySQL、Redis数据库
- 了解前端技术HTML、CSS、JavaScript
"""


async def test_direct_ollama_helper():
    """测试直接Ollama Helper"""
    try:
        logger.info("测试直接Ollama Helper...")
        
        from LLM.LLMManager import sys_llm_manager
        
        # 确保使用直接Ollama模式
        sys_llm_manager._use_direct_ollama = True
        sys_llm_manager._llm_map.clear()
        
        llm_helper = sys_llm_manager.get_generate_use_llm_helper()
        if llm_helper is None:
            logger.error("❌ 无法获取直接Ollama Helper")
            return False
        
        logger.info(f"✅ 直接Ollama Helper获取成功: {type(llm_helper).__name__}")
        
        # 测试获取LLM对象
        llm_obj = llm_helper.get_llm_object(0.3, 1024)
        logger.info(f"✅ 直接LLM对象创建成功: {type(llm_obj).__name__}")
        
        # 简单的调用测试
        logger.info("开始直接LLM调用...")
        result = llm_obj.invoke("请简单回答：你好")
        logger.info(f"✅ 直接LLM调用成功，结果: {result[:100] if len(str(result)) > 100 else result}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 直接Ollama Helper测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


async def test_direct_resume_processing():
    """测试直接简历处理"""
    try:
        logger.info("测试直接简历处理...")
        
        from LLM.LLMManager import sys_llm_manager
        from Agents.TalentAgentProcessing import TalentAgentProcessing
        
        # 确保使用直接Ollama模式
        sys_llm_manager._use_direct_ollama = True
        sys_llm_manager._llm_map.clear()
        
        processor = TalentAgentProcessing()
        
        logger.info("开始处理简历...")
        start_time = time.time()
        
        result = await processor._formatting(SAMPLE_RESUME)
        
        end_time = time.time()
        
        if result:
            logger.info(f"✅ 直接简历处理成功，耗时: {end_time - start_time:.2f}秒")
            logger.info(f"姓名: {result.userName}")
            logger.info(f"年龄: {result.age}")
            logger.info(f"性别: {result.sex}")
            logger.info(f"学历: {result.education}")
            return True
        else:
            logger.error("❌ 直接简历处理失败，返回None")
            return False
            
    except Exception as e:
        logger.error(f"❌ 直接简历处理测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


async def test_concurrent_resume_processing():
    """测试并发简历处理"""
    try:
        logger.info("测试并发简历处理...")
        
        from LLM.LLMManager import sys_llm_manager
        from Agents.TalentAgentProcessing import TalentAgentProcessing
        
        # 确保使用直接Ollama模式
        sys_llm_manager._use_direct_ollama = True
        sys_llm_manager._llm_map.clear()
        
        processor = TalentAgentProcessing()
        
        # 创建多个简历处理任务
        num_requests = 3
        tasks = []
        
        for i in range(num_requests):
            # 稍微修改简历内容以模拟不同的简历
            modified_resume = SAMPLE_RESUME.replace("张三", f"张三{i+1}")
            task = processor._formatting(modified_resume)
            tasks.append(task)
        
        logger.info(f"开始并发处理{num_requests}个简历...")
        start_time = time.time()
        
        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 统计结果
        success_count = sum(1 for r in results if not isinstance(r, Exception) and r is not None)
        error_count = len(results) - success_count
        
        logger.info(f"✅ 并发处理完成，总耗时: {total_time:.2f}秒")
        logger.info(f"成功: {success_count}, 失败: {error_count}")
        logger.info(f"平均每个请求耗时: {total_time / num_requests:.2f}秒")
        
        # 显示成功的结果
        for i, result in enumerate(results):
            if not isinstance(result, Exception) and result is not None:
                logger.info(f"请求{i+1} - 姓名: {result.userName}, 年龄: {result.age}")
            elif isinstance(result, Exception):
                logger.error(f"请求{i+1}失败: {str(result)}")
        
        return success_count > 0
        
    except Exception as e:
        logger.error(f"❌ 并发简历处理测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


async def main():
    """主测试函数"""
    logger.info("开始直接Ollama测试")
    
    # 测试直接Ollama Helper
    if not await test_direct_ollama_helper():
        logger.error("直接Ollama Helper测试失败，停止测试")
        return
    
    logger.info("等待2秒...")
    await asyncio.sleep(2)
    
    # 测试直接简历处理
    if not await test_direct_resume_processing():
        logger.error("直接简历处理测试失败")
        return
    
    logger.info("等待2秒...")
    await asyncio.sleep(2)
    
    # 测试并发简历处理
    if not await test_concurrent_resume_processing():
        logger.error("并发简历处理测试失败")
        return
    
    logger.info("✅ 所有直接Ollama测试通过")

if __name__ == "__main__":
    asyncio.run(main())
