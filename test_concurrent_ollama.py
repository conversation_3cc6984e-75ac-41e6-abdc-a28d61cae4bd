#!/usr/bin/env python3
"""
测试Ollama并发处理效果的脚本
"""

import asyncio
import time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Agents.TalentAgentProcessing import TalentAgentProcessing
from Utils.PerformanceMonitor import performance_monitor
from Utils.logs.LoggingConfig import logger


# 测试用的简历内容
SAMPLE_RESUME = """
姓名：张三
性别：男
年龄：28
学历：本科
邮箱：<EMAIL>
手机：13800138000
现居地址：北京市朝阳区
政治面貌：群众

求职意向：
期望职位：Python开发工程师
期望薪资：15-25K
期望城市：北京

教育经历：
2016-2020 北京理工大学 计算机科学与技术 本科

工作经历：
2020.07-2023.05 ABC科技有限公司 Python开发工程师
主要负责后端API开发，使用Django框架开发Web应用

项目经验：
1. 电商平台后端系统
   时间：2021.03-2022.08
   职责：负责订单模块和支付模块的开发
   技术栈：Python, Django, MySQL, Redis

2. 数据分析平台
   时间：2022.09-2023.05
   职责：负责数据处理和可视化功能开发
   技术栈：Python, Pandas, Flask, ECharts

技能：
- 熟练掌握Python、Django、Flask
- 熟悉MySQL、Redis数据库
- 了解前端技术HTML、CSS、JavaScript
- 熟悉Git版本控制
"""


async def test_single_processing():
    """测试单个简历处理"""
    logger.info("=== 开始单个简历处理测试 ===")
    
    processor = TalentAgentProcessing()
    start_time = time.time()
    
    try:
        result = await processor._formatting(SAMPLE_RESUME)
        end_time = time.time()
        
        if result:
            logger.info(f"单个简历处理成功，耗时: {end_time - start_time:.2f}秒")
            logger.info(f"解析结果 - 姓名: {result.userName}, 年龄: {result.age}")
        else:
            logger.error("单个简历处理失败")
            
    except Exception as e:
        logger.error(f"单个简历处理异常: {str(e)}")


async def test_concurrent_processing(num_requests: int = 5):
    """测试并发简历处理"""
    logger.info(f"=== 开始并发简历处理测试，并发数: {num_requests} ===")
    
    processor = TalentAgentProcessing()
    start_time = time.time()
    
    # 创建并发任务
    tasks = []
    for i in range(num_requests):
        # 稍微修改简历内容以模拟不同的简历
        modified_resume = SAMPLE_RESUME.replace("张三", f"张三{i+1}")
        task = processor._formatting(modified_resume)
        tasks.append(task)
    
    try:
        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # 统计结果
        success_count = sum(1 for r in results if not isinstance(r, Exception) and r is not None)
        error_count = len(results) - success_count
        
        logger.info(f"并发处理完成，总耗时: {end_time - start_time:.2f}秒")
        logger.info(f"成功: {success_count}, 失败: {error_count}")
        logger.info(f"平均每个请求耗时: {(end_time - start_time) / num_requests:.2f}秒")
        
        # 显示成功的结果
        for i, result in enumerate(results):
            if not isinstance(result, Exception) and result is not None:
                logger.info(f"请求{i+1} - 姓名: {result.userName}, 年龄: {result.age}")
            elif isinstance(result, Exception):
                logger.error(f"请求{i+1}失败: {str(result)}")
                
    except Exception as e:
        logger.error(f"并发处理异常: {str(e)}")


async def test_performance_comparison():
    """性能对比测试：顺序 vs 并发"""
    logger.info("=== 开始性能对比测试 ===")
    
    num_requests = 3
    processor = TalentAgentProcessing()
    
    # 测试顺序处理
    logger.info("测试顺序处理...")
    start_time = time.time()
    
    for i in range(num_requests):
        modified_resume = SAMPLE_RESUME.replace("张三", f"顺序张{i+1}")
        try:
            result = await processor._formatting(modified_resume)
            if result:
                logger.info(f"顺序处理{i+1}完成 - {result.userName}")
        except Exception as e:
            logger.error(f"顺序处理{i+1}失败: {str(e)}")
    
    sequential_time = time.time() - start_time
    logger.info(f"顺序处理总耗时: {sequential_time:.2f}秒")
    
    # 等待一下，让系统稳定
    await asyncio.sleep(2)
    
    # 测试并发处理
    logger.info("测试并发处理...")
    start_time = time.time()
    
    tasks = []
    for i in range(num_requests):
        modified_resume = SAMPLE_RESUME.replace("张三", f"并发张{i+1}")
        task = processor._formatting(modified_resume)
        tasks.append(task)
    
    try:
        results = await asyncio.gather(*tasks, return_exceptions=True)
        concurrent_time = time.time() - start_time
        
        success_count = sum(1 for r in results if not isinstance(r, Exception) and r is not None)
        logger.info(f"并发处理总耗时: {concurrent_time:.2f}秒，成功: {success_count}")
        
        # 计算性能提升
        if concurrent_time > 0:
            speedup = sequential_time / concurrent_time
            logger.info(f"性能提升: {speedup:.2f}x")
            logger.info(f"时间节省: {((sequential_time - concurrent_time) / sequential_time * 100):.1f}%")
        
    except Exception as e:
        logger.error(f"并发处理异常: {str(e)}")


async def main():
    """主测试函数"""
    logger.info("开始Ollama并发测试")
    
    try:
        # 单个处理测试
        await test_single_processing()
        await asyncio.sleep(1)
        
        # 并发处理测试
        await test_concurrent_processing(3)
        await asyncio.sleep(1)
        
        # 性能对比测试
        await test_performance_comparison()
        
        # 显示性能监控结果
        logger.info("\n=== 性能监控摘要 ===")
        performance_monitor.log_performance_summary()
        
    except Exception as e:
        logger.error(f"测试过程中出现异常: {str(e)}")
    
    logger.info("测试完成")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
