import asyncio
import concurrent.futures
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from Utils.logs.LoggingConfig import logger


class ConcurrentLLMHelper:
    """LLM并发调用助手类"""
    
    def __init__(self, max_workers: int = 5):
        """
        初始化并发助手
        
        Args:
            max_workers: 最大并发数，建议根据ollama配置的并发数设置
        """
        self.max_workers = max_workers
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._lock = threading.Lock()
    
    def concurrent_invoke(self, tasks: List[Dict[str, Any]]) -> List[Any]:
        """
        并发调用多个LLM任务（同步版本）
        
        Args:
            tasks: 任务列表，每个任务包含 {'llm': llm_object, 'input': input_data, 'name': task_name}
        
        Returns:
            结果列表，按任务顺序返回
        """
        if not tasks:
            return []
        
        logger.info(f"开始并发执行 {len(tasks)} 个LLM任务")
        
        # 提交所有任务
        future_to_task = {}
        for i, task in enumerate(tasks):
            future = self._executor.submit(
                self._safe_invoke, 
                task['llm'], 
                task['input'], 
                task.get('name', f'Task-{i}')
            )
            future_to_task[future] = i
        
        # 收集结果
        results = [None] * len(tasks)
        for future in as_completed(future_to_task):
            task_index = future_to_task[future]
            try:
                result = future.result()
                results[task_index] = result
                logger.info(f"任务 {task_index} 完成")
            except Exception as e:
                logger.error(f"任务 {task_index} 执行失败: {str(e)}")
                results[task_index] = None
        
        logger.info(f"所有LLM任务执行完成")
        return results
    
    async def async_concurrent_invoke(self, tasks: List[Dict[str, Any]]) -> List[Any]:
        """
        并发调用多个LLM任务（异步版本）
        
        Args:
            tasks: 任务列表，每个任务包含 {'llm': llm_object, 'input': input_data, 'name': task_name}
        
        Returns:
            结果列表，按任务顺序返回
        """
        if not tasks:
            return []
        
        logger.info(f"开始异步并发执行 {len(tasks)} 个LLM任务")
        
        # 创建异步任务
        async_tasks = []
        for i, task in enumerate(tasks):
            async_task = asyncio.create_task(
                self._async_safe_invoke(
                    task['llm'], 
                    task['input'], 
                    task.get('name', f'Task-{i}')
                )
            )
            async_tasks.append(async_task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*async_tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"异步任务 {i} 执行失败: {str(result)}")
                processed_results.append(None)
            else:
                processed_results.append(result)
                logger.info(f"异步任务 {i} 完成")
        
        logger.info(f"所有异步LLM任务执行完成")
        return processed_results
    
    def _safe_invoke(self, llm, input_data, task_name: str = "Unknown"):
        """安全的LLM调用，包含异常处理"""
        try:
            logger.debug(f"开始执行任务: {task_name}")
            result = llm.invoke(input_data)
            logger.debug(f"任务 {task_name} 执行成功")
            return result
        except Exception as e:
            logger.error(f"任务 {task_name} 执行失败: {str(e)}")
            raise e
    
    async def _async_safe_invoke(self, llm, input_data, task_name: str = "Unknown"):
        """异步安全的LLM调用"""
        try:
            logger.debug(f"开始执行异步任务: {task_name}")
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(self._executor, llm.invoke, input_data)
            logger.debug(f"异步任务 {task_name} 执行成功")
            return result
        except Exception as e:
            logger.error(f"异步任务 {task_name} 执行失败: {str(e)}")
            raise e
    
    def batch_process(self, items: List[Any], process_func: Callable, batch_size: int = None) -> List[Any]:
        """
        批量处理数据
        
        Args:
            items: 要处理的数据列表
            process_func: 处理函数，接收单个item，返回处理结果
            batch_size: 批次大小，默认为max_workers
        
        Returns:
            处理结果列表
        """
        if not items:
            return []
        
        if batch_size is None:
            batch_size = self.max_workers
        
        logger.info(f"开始批量处理 {len(items)} 个项目，批次大小: {batch_size}")
        
        results = []
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            batch_tasks = []
            
            for j, item in enumerate(batch):
                task = {
                    'llm': None,  # 这里需要根据实际情况设置
                    'input': item,
                    'name': f'Batch-{i//batch_size}-Item-{j}'
                }
                batch_tasks.append(task)
            
            # 处理当前批次
            batch_results = []
            futures = []
            for task in batch_tasks:
                future = self._executor.submit(process_func, task['input'])
                futures.append(future)
            
            for future in as_completed(futures):
                try:
                    result = future.result()
                    batch_results.append(result)
                except Exception as e:
                    logger.error(f"批处理项目失败: {str(e)}")
                    batch_results.append(None)
            
            results.extend(batch_results)
            logger.info(f"批次 {i//batch_size + 1} 处理完成")
        
        logger.info(f"批量处理完成，共处理 {len(results)} 个项目")
        return results
    
    def shutdown(self):
        """关闭线程池"""
        self._executor.shutdown(wait=True)
        logger.info("ConcurrentLLMHelper 已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.shutdown()


# 全局并发助手实例
concurrent_llm_helper = ConcurrentLLMHelper(max_workers=5)
