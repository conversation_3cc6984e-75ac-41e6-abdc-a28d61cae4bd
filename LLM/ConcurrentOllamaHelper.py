import asyncio
import aiohttp
from typing import Optional, Dict, Any
from langchain_core.language_models import BaseLLM, BaseChatModel
from langchain_ollama import ChatOllama, OllamaLLM
import threading
import time

from LLM.BaseLLMHelper import BaseLLMHelper
from Models.peewee.OrmModel import KBModel
from Utils.logs.LoggingConfig import logger


class ConcurrentOllamaHelper(BaseLLMHelper):
    """支持高并发的 Ollama LLM Helper"""
    
    def __init__(self, model: KBModel = None, max_concurrent: int = 10):
        super().__init__(model)
        self.max_concurrent = max_concurrent
        self._session_pool: Optional[aiohttp.ClientSession] = None
        self._llm_cache: Dict[str, Any] = {}
        self._semaphore = asyncio.Semaphore(max_concurrent)
        self._lock = threading.Lock()
        
        # 连接池配置
        self._connector_config = {
            'limit': max_concurrent * 2,  # 总连接数
            'limit_per_host': max_concurrent,  # 每个主机连接数
            'keepalive_timeout': 60,  # 保持连接时间
            'enable_cleanup_closed': True,
            'ttl_dns_cache': 300,  # DNS缓存时间
            'use_dns_cache': True,
        }
        
        logger.info(f"初始化并发Ollama Helper，最大并发数: {max_concurrent}")
    
    def get_llm_object(self, temperature: float, num_ctx: int = None) -> BaseLLM:
        """获取 LLM 对象，支持连接复用"""
        if num_ctx is None:
            num_ctx = self.get_max_tokens()
        
        # 创建缓存键
        cache_key = f"llm_{temperature}_{num_ctx}"
        
        with self._lock:
            if cache_key not in self._llm_cache:
                llm = OllamaLLM(
                    base_url=self._model.api_url,
                    model=self._model.model_name,
                    temperature=temperature,
                    num_ctx=num_ctx,
                    # 优化并发配置
                    timeout=120,  # 增加超时时间
                    keep_alive="10m",  # 保持模型在内存中更长时间
                    # 如果支持，添加更多并发优化配置
                )
                self._llm_cache[cache_key] = llm
                logger.debug(f"创建新的 LLM 对象: {cache_key}")
        
        return self._llm_cache[cache_key]

    def get_llm_chat_object(self, temperature: float, num_ctx: int = None) -> BaseChatModel:
        """获取聊天 LLM 对象，支持连接复用"""
        if num_ctx is None:
            num_ctx = self.get_max_tokens()
        
        # 创建缓存键
        cache_key = f"chat_{temperature}_{num_ctx}"
        
        with self._lock:
            if cache_key not in self._llm_cache:
                llm = ChatOllama(
                    base_url=self._model.api_url,
                    model=self._model.model_name,
                    temperature=temperature,
                    num_ctx=num_ctx,
                    # 优化并发配置
                    timeout=120,  # 增加超时时间
                    keep_alive="10m",  # 保持模型在内存中更长时间
                    # 添加重试配置
                    num_retries=3,
                )
                self._llm_cache[cache_key] = llm
                logger.debug(f"创建新的聊天 LLM 对象: {cache_key}")
        
        return self._llm_cache[cache_key]
    
    async def get_session_pool(self) -> aiohttp.ClientSession:
        """获取 HTTP 会话池"""
        if self._session_pool is None or self._session_pool.closed:
            connector = aiohttp.TCPConnector(**self._connector_config)
            
            timeout = aiohttp.ClientTimeout(
                total=120,  # 总超时时间
                connect=30,  # 连接超时
                sock_read=60  # 读取超时
            )
            
            self._session_pool = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            )
            logger.info(f"创建 HTTP 连接池，配置: {self._connector_config}")
        
        return self._session_pool
    
    async def concurrent_invoke(self, runnable, content: str) -> Any:
        """并发安全的调用方法"""
        async with self._semaphore:  # 限制并发数
            try:
                start_time = time.time()
                result = runnable.invoke({"content": content})
                end_time = time.time()
                logger.debug(f"LLM调用完成，耗时: {end_time - start_time:.2f}秒")
                return result
            except Exception as e:
                logger.error(f"LLM并发调用失败: {str(e)}")
                raise
    
    async def batch_invoke(self, runnables_and_contents: list) -> list:
        """批量并发调用"""
        tasks = []
        for runnable, content in runnables_and_contents:
            task = self.concurrent_invoke(runnable, content)
            tasks.append(task)
        
        logger.info(f"开始批量并发调用，任务数: {len(tasks)}")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计成功和失败的数量
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        error_count = len(results) - success_count
        logger.info(f"批量调用完成，成功: {success_count}, 失败: {error_count}")
        
        return results
    
    async def close(self):
        """关闭连接池"""
        if self._session_pool and not self._session_pool.closed:
            await self._session_pool.close()
            logger.info("HTTP连接池已关闭")
    
    def __del__(self):
        """析构函数，确保资源清理"""
        if hasattr(self, '_session_pool') and self._session_pool and not self._session_pool.closed:
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.close())
                else:
                    loop.run_until_complete(self.close())
            except Exception as e:
                logger.warning(f"清理连接池时出错: {str(e)}")
