import asyncio
import aiohttp
from typing import Optional, Dict, Any
from langchain_core.language_models import BaseLLM, BaseChatModel
from langchain_ollama import ChatOllama, OllamaLLM

from LLM.BaseLLMHelper import BaseLLMHelper
from Models.peewee.OrmModel import KBModel
from Utils.logs.LoggingConfig import logger


class EnhancedOllamaLLMHelper(BaseLLMHelper):
    """增强的 Ollama LLM Helper，支持更好的并发处理"""
    
    def __init__(self, model: KBModel = None, connection_pool_size: int = 10):
        super().__init__(model)
        self.connection_pool_size = connection_pool_size
        self._session_pool: Optional[aiohttp.ClientSession] = None
        self._llm_cache: Dict[str, Any] = {}
    
    def get_llm_object(self, temperature: float, num_ctx: int = None) -> BaseLLM:
        """获取 LLM 对象，支持连接池"""
        if num_ctx is None:
            num_ctx = self.get_max_tokens()
        
        # 创建缓存键
        cache_key = f"llm_{temperature}_{num_ctx}"
        
        if cache_key not in self._llm_cache:
            llm = OllamaLLM(
                base_url=self._model.api_url,
                model=self._model.model_name,
                temperature=temperature,
                num_ctx=num_ctx,
                # 添加并发相关配置
                timeout=60,  # 增加超时时间
                # 如果 langchain-ollama 支持，可以添加更多并发配置
            )
            self._llm_cache[cache_key] = llm
            logger.debug(f"创建新的 LLM 对象: {cache_key}")
        
        return self._llm_cache[cache_key]

    def get_llm_chat_object(self, temperature: float, num_ctx: int = None) -> BaseChatModel:
        """获取聊天 LLM 对象，支持连接池"""
        if num_ctx is None:
            num_ctx = self.get_max_tokens()
        
        # 创建缓存键
        cache_key = f"chat_{temperature}_{num_ctx}"
        
        if cache_key not in self._llm_cache:
            llm = ChatOllama(
                base_url=self._model.api_url,
                model=self._model.model_name,
                temperature=temperature,
                num_ctx=num_ctx,
                # 添加并发相关配置
                timeout=60,  # 增加超时时间
                # 如果支持，添加更多并发配置
                keep_alive="5m",  # 保持模型在内存中
            )
            self._llm_cache[cache_key] = llm
            logger.debug(f"创建新的聊天 LLM 对象: {cache_key}")
        
        return self._llm_cache[cache_key]
    
    async def get_session_pool(self) -> aiohttp.ClientSession:
        """获取 HTTP 会话池"""
        if self._session_pool is None or self._session_pool.closed:
            connector = aiohttp.TCPConnector(
                limit=self.connection_pool_size,  # 总连接池大小
                limit_per_host=self.connection_pool_size,  # 每个主机的连接数
                keepalive_timeout=30,  # 保持连接时间
                enable_cleanup_closed=True
            )
            
            timeout = aiohttp.ClientTimeout(total=60)  # 总超时时间
            
            self._session_pool = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            )
            logger.info(f"创建 HTTP 连接池，大小: {self.connection_pool_size}")
        
        return self._session_pool
    
    async def concurrent_invoke(self, inputs: list, temperature: float = 0.3, num_ctx: int = None):
        """并发调用多个请求"""
        if not inputs:
            return []
        
        logger.info(f"开始并发调用 {len(inputs)} 个请求")
        
        # 获取 LLM 对象
        llm = self.get_llm_chat_object(temperature, num_ctx)
        
        # 创建并发任务
        tasks = []
        for i, input_data in enumerate(inputs):
            task = asyncio.create_task(
                self._safe_async_invoke(llm, input_data, f"Task-{i}")
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"任务 {i} 失败: {str(result)}")
                processed_results.append(None)
            else:
                processed_results.append(result)
        
        logger.info(f"并发调用完成，成功: {len([r for r in processed_results if r is not None])}")
        return processed_results
    
    async def _safe_async_invoke(self, llm, input_data, task_name: str):
        """安全的异步调用"""
        try:
            logger.debug(f"开始执行任务: {task_name}")
            
            # 在事件循环中运行同步的 LLM 调用
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, llm.invoke, input_data)
            
            logger.debug(f"任务 {task_name} 执行成功")
            return result
            
        except Exception as e:
            logger.error(f"任务 {task_name} 执行失败: {str(e)}")
            raise e
    
    def clear_cache(self):
        """清理 LLM 缓存"""
        self._llm_cache.clear()
        logger.info("LLM 缓存已清理")
    
    async def close(self):
        """关闭资源"""
        if self._session_pool and not self._session_pool.closed:
            await self._session_pool.close()
            logger.info("HTTP 连接池已关闭")
        
        self.clear_cache()
    
    def __del__(self):
        """析构函数"""
        try:
            if self._session_pool and not self._session_pool.closed:
                asyncio.create_task(self._session_pool.close())
        except:
            pass


# 使用示例和配置建议
class OllamaConfigOptimizer:
    """Ollama 配置优化建议"""
    
    @staticmethod
    def get_recommended_config():
        """获取推荐的 Ollama 配置"""
        return {
            "OLLAMA_NUM_PARALLEL": "4",  # 并行请求数
            "OLLAMA_MAX_LOADED_MODELS": "3",  # 最大加载模型数
            "OLLAMA_FLASH_ATTENTION": "1",  # 启用 Flash Attention
            "OLLAMA_HOST": "0.0.0.0:11434",  # 监听地址
            "OLLAMA_KEEP_ALIVE": "5m",  # 模型保持时间
            "OLLAMA_MAX_QUEUE": "512",  # 最大队列长度
        }
    
    @staticmethod
    def print_optimization_tips():
        """打印优化建议"""
        tips = [
            "1. 设置环境变量 OLLAMA_NUM_PARALLEL=4 以支持并行请求",
            "2. 增加 OLLAMA_MAX_QUEUE=512 以支持更多排队请求",
            "3. 设置 OLLAMA_KEEP_ALIVE=5m 保持模型在内存中",
            "4. 确保有足够的 GPU 内存支持并发推理",
            "5. 考虑使用多个 Ollama 实例进行负载均衡",
            "6. 监控系统资源使用情况，避免过度并发",
        ]
        
        print("Ollama 并发优化建议:")
        for tip in tips:
            print(f"  {tip}")
