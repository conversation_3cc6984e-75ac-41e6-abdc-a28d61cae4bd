from typing import Dict

from LLM.BaseLLMHelper import <PERSON><PERSON><PERSON>Helper
from LLM.EmbeddingsManager import EmbeddingsManager
from LLM.OllamaLLMHelper import <PERSON>lla<PERSON><PERSON><PERSON>Helper
from LLM.ConcurrentOllamaHelper import ConcurrentOllamaHelper
from LLM.DirectOllamaHelper import DirectOllamaHelper
from LLM.OpenAiLLMHelper import OpenAi<PERSON>MHelper
from Models.peewee.OrmModel import KBModel, ModelsPlatformName
from Services.SqlServer.KBModelsService import KBModelsService
from Configs.ConcurrencyConfig import CURRENT_CONFIG


class LLMManager:
    _llm_map: Dict[int, BaseLLMHelper] = {}
    _use_concurrent_ollama: bool = True  # 是否使用并发版本的Ollama
    _use_direct_ollama: bool = True  # 是否使用直接Ollama调用（绕过LangChain）

    @staticmethod
    def __create_llm_helper(*, id: int = None, model: KBModel = None, use_concurrent: bool = True) -> BaseLLMHelper | None:
        if id:
            model = KBModel.get_by_id(id)
        if model.platform_type == ModelsPlatformName.OLLAMA.value:
            # 优先使用直接Ollama调用（绕过LangChain问题）
            if LLMManager._use_direct_ollama:
                max_concurrent = CURRENT_CONFIG.get("max_concurrent_requests", 8)
                return DirectOllamaHelper(model, max_concurrent)
            elif use_concurrent:
                max_concurrent = CURRENT_CONFIG.get("max_concurrent_requests", 8)
                return ConcurrentOllamaHelper(model, max_concurrent)
            else:
                return OllamaLLMHelper(model)
        elif model.platform_type == ModelsPlatformName.OPENAI.value:
            return OpenAiLLMHelper(model)
        elif model.platform_type == ModelsPlatformName.DEEPSEEK.value:
            return OpenAiLLMHelper(model)
        elif model.platform_type == ModelsPlatformName.SPARK.value:
            return OpenAiLLMHelper(model)
        elif model.platform_type == ModelsPlatformName.QWEN.value:
            return OpenAiLLMHelper(model)
        return None

    def get_llm_helper(self, *, id: int = None, model: KBModel = None, use_concurrent: bool = None) -> BaseLLMHelper | None:
        # 如果没有指定use_concurrent，使用类默认设置
        if use_concurrent is None:
            use_concurrent = self._use_concurrent_ollama

        # 为并发版本创建不同的缓存键
        cache_key = id if id else (model.id if model else None)
        if use_concurrent and cache_key:
            cache_key = f"concurrent_{cache_key}"

        llm_helper = self._llm_map.get(cache_key, None)
        if llm_helper is None:
            llm_helper = self.__create_llm_helper(id=id, model=model, use_concurrent=use_concurrent)
            if llm_helper is not None:
                self._llm_map[cache_key] = llm_helper
        return llm_helper

    def get_chat_use_llm_helper(self) -> BaseLLMHelper | None:
        return self.get_llm_helper(model=KBModelsService.get_default_chat_model())

    def get_generate_use_llm_helper(self) -> BaseLLMHelper | None:
        return self.get_llm_helper(model=KBModelsService.get_generate_model())


sys_llm_manager = LLMManager()
embeddings_manager = EmbeddingsManager()
