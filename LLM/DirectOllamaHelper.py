import asyncio
import aiohttp
import requests
import json
import threading
import time
from typing import Optional, Dict, Any, List
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor

from LLM.BaseLLMHelper import BaseLLMHelper
from Models.peewee.OrmModel import KBModel
from Utils.logs.LoggingConfig import logger


class DirectOllamaLLM:
    """直接调用Ollama API的LLM类，绕过LangChain"""
    
    def __init__(self, base_url: str, model: str, temperature: float = 0.3, num_ctx: int = 4096, timeout: int = 120):
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.temperature = temperature
        self.num_ctx = num_ctx
        self.timeout = timeout
        
    def invoke(self, prompt: str) -> str:
        """同步调用"""
        try:
            url = f"{self.base_url}/api/generate"
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_ctx": self.num_ctx
                }
            }
            
            response = requests.post(url, json=payload, timeout=self.timeout)
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                raise Exception(f"Ollama API error: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"DirectOllamaLLM调用失败: {str(e)}")
            raise
    
    async def ainvoke(self, prompt: str) -> str:
        """异步调用"""
        try:
            url = f"{self.base_url}/api/generate"
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_ctx": self.num_ctx
                }
            }
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get('response', '')
                    else:
                        text = await response.text()
                        raise Exception(f"Ollama API error: {response.status} - {text}")
                        
        except Exception as e:
            logger.error(f"DirectOllamaLLM异步调用失败: {str(e)}")
            raise


class DirectOllamaHelper(BaseLLMHelper):
    """直接调用Ollama API的Helper，支持高并发"""
    
    def __init__(self, model: KBModel = None, max_concurrent: int = 10):
        super().__init__(model)
        self.max_concurrent = max_concurrent
        self._llm_cache: Dict[str, DirectOllamaLLM] = {}
        self._semaphore = asyncio.Semaphore(max_concurrent)
        self._lock = threading.Lock()
        self._executor = ThreadPoolExecutor(max_workers=max_concurrent, thread_name_prefix="direct-ollama")
        
        logger.info(f"初始化直接Ollama Helper，最大并发数: {max_concurrent}")
    
    def get_llm_object(self, temperature: float, num_ctx: int = None) -> DirectOllamaLLM:
        """获取直接LLM对象"""
        if num_ctx is None:
            num_ctx = self.get_max_tokens()
        
        # 创建缓存键
        cache_key = f"llm_{temperature}_{num_ctx}"
        
        with self._lock:
            if cache_key not in self._llm_cache:
                llm = DirectOllamaLLM(
                    base_url=self._model.api_url,
                    model=self._model.model_name,
                    temperature=temperature,
                    num_ctx=num_ctx,
                    timeout=120
                )
                self._llm_cache[cache_key] = llm
                logger.debug(f"创建新的直接LLM对象: {cache_key}")
        
        return self._llm_cache[cache_key]

    def get_llm_chat_object(self, temperature: float, num_ctx: int = None) -> DirectOllamaLLM:
        """获取聊天LLM对象（与普通LLM相同）"""
        return self.get_llm_object(temperature, num_ctx)
    
    async def concurrent_invoke(self, llm: DirectOllamaLLM, prompt: str) -> str:
        """并发安全的调用方法"""
        async with self._semaphore:  # 限制并发数
            try:
                start_time = time.time()
                
                # 在线程池中执行同步调用
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(self._executor, llm.invoke, prompt)
                
                end_time = time.time()
                logger.debug(f"直接LLM调用完成，耗时: {end_time - start_time:.2f}秒")
                return result
            except Exception as e:
                logger.error(f"直接LLM并发调用失败: {str(e)}")
                raise
    
    async def batch_invoke(self, llm_prompt_pairs: List[tuple]) -> List[str]:
        """批量并发调用"""
        tasks = []
        for llm, prompt in llm_prompt_pairs:
            task = self.concurrent_invoke(llm, prompt)
            tasks.append(task)
        
        logger.info(f"开始批量并发调用，任务数: {len(tasks)}")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计成功和失败的数量
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        error_count = len(results) - success_count
        logger.info(f"批量调用完成，成功: {success_count}, 失败: {error_count}")
        
        return results
    
    def close(self):
        """关闭线程池"""
        if hasattr(self, '_executor'):
            self._executor.shutdown(wait=True)
            logger.info("直接Ollama线程池已关闭")


class DirectOllamaRunnable:
    """模拟LangChain Runnable接口的包装器"""
    
    def __init__(self, llm: DirectOllamaLLM):
        self.llm = llm
    
    def invoke(self, inputs: dict) -> str:
        """同步调用，兼容LangChain接口"""
        if isinstance(inputs, dict):
            # 从字典中提取内容
            content = inputs.get('content', '') or inputs.get('input', '') or str(inputs)
        else:
            content = str(inputs)
        
        return self.llm.invoke(content)
    
    async def ainvoke(self, inputs: dict) -> str:
        """异步调用，兼容LangChain接口"""
        if isinstance(inputs, dict):
            content = inputs.get('content', '') or inputs.get('input', '') or str(inputs)
        else:
            content = str(inputs)
        
        return await self.llm.ainvoke(content)


def create_direct_ollama_runnable(base_url: str, model: str, temperature: float = 0.3, num_ctx: int = 4096) -> DirectOllamaRunnable:
    """创建直接Ollama Runnable对象的工厂函数"""
    llm = DirectOllamaLLM(base_url, model, temperature, num_ctx)
    return DirectOllamaRunnable(llm)
