import re
import threading
from datetime import datetime, timedelta

import requests
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate, \
    ChatPromptTemplate
from langchain_core.runnables import <PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>
from langchain_core.runnables.history import RunnableWithMessageHistory

from Configs.Config import SysConfig
from LLM.LLMManager import sys_llm_manager
from Models.agent.ResumeInfo import ResumeInfo
from Models.agent.TalentInfoProcessing import ContentInfo
from Models.dto.TaskInfoDto import TaskInfoDto
from Utils.StringUtils import StringUtils
from Utils.logs.LoggingConfig import logger
from Utils.CommonUtils import from_json_str, remove_think_tags
from Agents.TalentAgentProcessing import TalentAgentProcessing


class TalentAgent:
    _llm_name: str | None = "DEFAULT"

    def __init__(self):
        self.__temperature = SysConfig["agents"]["kb_agent"]["temperature"]
        self.__talent_url = SysConfig["talent"]["talent_url"]
        self.__tokens = SysConfig["talent"]["tokens"]
        # 使用TalentAgentProcessing进行解析
        self.__talent_processor = TalentAgentProcessing()

    async def chat_for_answer(self, content: str, job_id: int = None):
        try:
            try:
                task_dto = self.getPostingInfo(job_id)
                if task_dto is None:
                    logger.error(f"任务id:{job_id},该任务信息不存在")
                    # 终止执行
                    return 0

                taskInfo = TaskInfoDto.from_dict(task_dto)
                # 使用TalentAgentProcessing进行解析
                talent_info = self._formatting(content, taskInfo.screeningConditions)
                if talent_info is None:
                    logger.error(f"简历解析失败，任务id:{job_id}")
                    return 0
                
                # 过滤校验
                code = self.condition_filter(talent_info, job_id, taskInfo)
                if code == 0:
                    return code

                # 更新值
                talent_info.jobId = job_id
                if talent_info.sex != "男" and talent_info.sex != "女":
                    talent_info.sex = ""
                # 启动另一个线程去处理
                threading.Thread(target=self.__saveConversationMemory(talent_info), daemon=True).start()
            except Exception as e:
                print(f"Error in streaming response: {str(e)}")
                # raise e
                return 0
            return code

        except Exception as e:
            print(f"Error in chat_for_answer: {str(e)}")
            # raise e
            return 0

    async def _formatting(self, content: str, condition: str = None) -> ResumeInfo:
        """
        使用TalentAgentProcessing进行简历解析（支持异步并发）
        """
        try:
            logger.info(f"开始使用TalentAgentProcessing异步解析简历")
            talent_info = await self.__talent_processor._formatting(content, condition)
            logger.info(f"简历异步解析完成：{talent_info}")
            return talent_info
        except Exception as e:
            logger.error(f"简历解析失败: {str(e)}")
            return None

    # 请求java端存储数据
    def __saveConversationMemory(self, talent_info: ResumeInfo):
        json_string = talent_info.model_dump_json(indent=4)
        logger.info(json_string)
        # 设置请求头，指定内容类型为 JSON
        headers = {
            'Content-Type': 'application/json',
        }
        # 发送 POST 请求到 Java API
        try:
            response = requests.post(f'{self.__talent_url}/talentbase/agentManage', headers=headers, data=json_string)
            # 检查响应状态码
            if response.status_code == 200:
                logger.info("请求成功！")
                response_body = response.json()
                if response_body.get("code") == 200:
                    # 删除已发送的数据
                    logger.info("数据保存成功！")
                else:
                    logger.info("数据保存失败！")
                logger.info("响应内容：", response.json())
            else:
                logger.info("响应内容：", response.text)
        except requests.exceptions.RequestException as e:
            logger.error(f"请求过程中出现错误：{e}")

    # 请求java端获取数据
    def getPostingInfo(self, job_id: int = None):
        # 设置请求头，指定内容类型为 JSON
        headers = {
            'Content-Type': 'application/json',
        }
        # 发送 Get 请求到 Java API
        try:
            response = requests.get(f'{self.__talent_url}/talentbase/agentManage/getPostingInfo/{job_id}',
                                    headers=headers)
            # 检查响应状态码
            if response.status_code == 200:
                response_body = response.json()
                if response_body.get("code") == 200:
                    data = response.json()
                    return data.get("data")
                else:
                    logger.info("数据保存失败！")
            else:
                logger.info(f"请求失败，状态码：{response.status_code}")
                logger.info("响应内容：", response.text)
        except requests.exceptions.RequestException as e:
            logger.error(f"请求过程中出现错误：{e}")

    def condition_filter(self, talent_info: ResumeInfo, job_id: int = None, requirements: TaskInfoDto = None):
        if not requirements:
            return 0

        # 计算总分
        total_score = self.calculate_total_score(talent_info, requirements)
        talent_info.totalScore = total_score

        # 定义返回码
        code = 0

        # 执行各项过滤检查
        if (
                self.education_filter(talent_info, job_id, requirements) and
                self.experience_filter(talent_info, job_id, requirements) and
                self.job_hopping_filter(talent_info, job_id, requirements) and
                self.salary_filter(talent_info, job_id, requirements) and
                self.pass_score_filter(total_score, requirements)
        ):
            logger.info(f"{talent_info.userName}用户通过筛选")
            code = 1

        return code

    @staticmethod
    def calculate_total_score(talent_info: ResumeInfo, requirements: TaskInfoDto) -> float:
        """计算候选人的总分。"""
        total_weight = (
                requirements.educationWeight +
                requirements.workExperienceWeight +
                requirements.jobHoppingRateWeight +
                requirements.salaryRangeWeight
        )
        if total_weight == 0:
            return 0

        divide = 100
        # 计算各项得分Weight 是百分制
        talent_info.minimumEducationScore = round(
            requirements.educationWeight * talent_info.minimumEducationScore / total_weight, 1)
        talent_info.workExperienceScore = round(
            requirements.workExperienceWeight * talent_info.workExperienceScore / total_weight, 1)
        talent_info.jobHoppingRateScore = round(
            requirements.jobHoppingRateWeight * talent_info.jobHoppingRateScore / total_weight, 1)
        talent_info.salaryRangeScore = round(
            requirements.salaryRangeWeight * talent_info.salaryRangeScore / total_weight, 1)

        total_score = (
                talent_info.minimumEducationScore +
                talent_info.workExperienceScore +
                talent_info.jobHoppingRateScore +
                talent_info.salaryRangeScore
        )

        return round(total_score, 1)

    @staticmethod
    def education_filter(talent_info: ResumeInfo, job_id: int, requirements: TaskInfoDto) -> bool:
        """执行学历过滤检查。"""
        if talent_info.education is not None and requirements.minimumEducation is not None:
            education_code = StringUtils.match_key_and_get_value(talent_info.education, requirements.educationMap)
            if education_code is not None and int(education_code) < requirements.minimumEducation:
                logger.error(
                    f"任务id:{job_id},该任务要求的学历为:{requirements.minimumEducation},用户的学历为:{education_code}")
                return False
        return True

    def experience_filter(self, talent_info: ResumeInfo, job_id: int, requirements: TaskInfoDto) -> bool:
        """执行工作经验过滤检查。"""
        if not self.is_value_within_range(
                talent_info.yearsOfExperience,
                requirements.experienceLowerBound,
                requirements.experienceUpperBound
        ):
            logger.error(
                f"任务id:{job_id},该任务要求的工作经验为:{requirements.experienceLowerBound}-{requirements.experienceUpperBound},"
                f"用户的工作经验为:{talent_info.yearsOfExperience}"
            )
            return False
        return True

    def job_hopping_filter(self, talent_info: ResumeInfo, job_id: int, requirements: TaskInfoDto) -> bool:
        """
           判断是否符合"最近几年跳槽几次"的条件。
           :param job_experience_list: 工作经历列表，每个元素包含 'startTime' 和 'endTime'
           :param years: 最近几年的范围
           :param max_hops: 最大跳槽次数
           :return: 如果符合要求返回 True，否则返回 False
           """
        if requirements.jobHoppingYears is None or requirements.jobHoppingCount is None:
            return True
        # 当前时间
        now = datetime.now()
        # 提取工作时间段
        work_periods = []
        for experience in talent_info.tbWorkExperienceList:
            start_time = datetime.strptime(experience.startTime, '%Y-%m')
            end_time = datetime.strptime(experience.endTime, '%Y-%m')
            work_periods.append((start_time, end_time))

        # 按结束时间排序（从早到晚）
        work_periods.sort(key=lambda x: x[1])
        logger.error(f"{talent_info.userName}用户的工作经历为:{work_periods}")

        # 计算最近几年内的跳槽次数
        years_ago = now - timedelta(days=requirements.jobHoppingYears * 365)
        job_hopping_count = 0
        for i in range(len(work_periods) - 1):  # 遍历工作经历
            if work_periods[i][1] >= years_ago:  # 如果结束时间在最近几年内
                job_hopping_count += 1

        # 判断是否符合要求
        if job_hopping_count <= requirements.jobHoppingCount:
            logger.info(
                f"任务id:{job_id},该任务要求的跳槽次数为:{requirements.jobHoppingCount},用户的跳槽次数为:{job_hopping_count}"
                f"执行成功"
            )
            return True
        logger.error(
            f"任务id:{job_id},该任务要求的跳槽次数为:{requirements.jobHoppingCount},用户的跳槽次数为:{job_hopping_count}"
        )
        return False

    def salary_filter(self, talent_info: ResumeInfo, job_id: int, requirements: TaskInfoDto) -> bool:
        """执行薪资范围过滤检查。"""
        UserSalaryRangeLowerBound = (
            talent_info.salaryRangeLowerBound / 1000 if talent_info.salaryRangeLowerBound >= 1000 else talent_info.salaryRangeLowerBound)
        UserSalaryRangeUpperBound = (
            talent_info.salaryRangeUpperBound / 1000 if talent_info.salaryRangeUpperBound >= 1000 else talent_info.salaryRangeUpperBound)
        if not (
                self.is_value_within_range(
                    UserSalaryRangeLowerBound,
                    requirements.salaryRangeLowerBound,
                    requirements.salaryRangeUpperBound
                ) or
                self.is_value_within_range(
                    UserSalaryRangeUpperBound,
                    requirements.salaryRangeLowerBound,
                    requirements.salaryRangeUpperBound
                )
        ):
            logger.error(
                f"任务id:{job_id},该任务要求的薪资范围为:{requirements.salaryRangeLowerBound}-{requirements.salaryRangeUpperBound},"
                f"用户的薪资范围为:{talent_info.salaryRangeLowerBound}-{talent_info.salaryRangeUpperBound}"
            )
            return False
        return True

    @staticmethod
    def pass_score_filter(total_score: float, requirements: TaskInfoDto) -> bool:
        """执行总分是否合格的检查。"""
        if requirements.passScore is None or total_score >= requirements.passScore:
            logger.info(f"用户的总分为:{total_score},通过筛选")
            return True
        logger.error(f"用户的总分为:{total_score},不通过筛选")
        return False

    # 判断一个值是否在指定区间内（包括边界）。
    @staticmethod
    def is_value_within_range(value, lower_bound, upper_bound):
        """
        判断一个值是否在指定区间内（包括边界）。
        :param value: 需要判断的值
        :param lower_bound: 区间下限
        :param upper_bound: 区间上限
        :return: 如果值在区间内，返回True；否则返回False
        """
        if lower_bound is None or upper_bound is None or value is None:
            # 如果区间边界未定义，返回true，因为没有边界限制
            return True
        return lower_bound <= value <= upper_bound

    # 请求java端存储数据
    def saveAnalyseTaskInfo(self, talent_info: ResumeInfo) -> bool:
        """保存分析任务信息到后端
        
        :param talent_info: 人才信息对象
        :return: 保存成功返回True，失败返回False
        """
        json_string = talent_info.model_dump_json(indent=4)
        logger.info(json_string)
        # 设置请求头，指定内容类型为 JSON
        headers = {
            'Content-Type': 'application/json',
        }
        # 发送 POST 请求到 Java API
        try:
            response = requests.post(f'{self.__talent_url}/talentbase/agentManageAnalyse', headers=headers,
                                     data=json_string)
            # 检查响应状态码
            if response.status_code == 200:
                logger.info("请求成功！")
                response_body = response.json()
                if response_body.get("code") == 200:
                    # 删除已发送的数据
                    logger.info("数据保存成功！")
                    return True
                else:
                    logger.info("数据保存失败！")
                    return False
                logger.info("响应内容：", response.json())
            else:
                logger.info("响应内容：", response.text)
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"请求过程中出现错误：{e}")
            return False

    # 请求java端获取数据
    def getAnalyseTaskInfo(self, job_id: int = None):
        # 设置请求头，指定内容类型为 JSON
        headers = {
            'Content-Type': 'application/json',
        }
        # 发送 Get 请求到 Java API
        try:
            response = requests.get(f'{self.__talent_url}/talentbase/agentManageAnalyse/getTaskInfo/{job_id}',
                                    headers=headers)
            # 检查响应状态码
            if response.status_code == 200:
                response_body = response.json()
                if response_body.get("code") == 200:
                    data = response.json()
                    return data.get("data")
                else:
                    logger.info("获取数据失败！")
            else:
                logger.info(f"请求失败，状态码：{response.status_code}")
                logger.info("响应内容：", response.text)
        except requests.exceptions.RequestException as e:
            logger.error(f"请求过程中出现错误：{e}")

    # 更新文件解析状态 0成功，1解析中，2失败
    def updateFileStatus(self, id: int, status: int):
        """调用后端接口更新解析文件状态

        :param task_id: 任务/文件 ID
        :param status: 状态码（0=成功，1=解析中，2=失败）
        """
        if id is None:
            logger.warning("updateFileStatus called with None id, ignore.")
            return

        headers = {
            'Content-Type': 'application/json',
        }
        try:
            url = f"{self.__talent_url}/talentbase/agentManageAnalyse/updateFileStatus/{id}/{status}"
            response = requests.put(url, headers=headers)

            if response.status_code == 200:
                logger.info(f"文件状态更新成功 id={id} status={status}")
            else:
                logger.error(f"文件状态更新失败 id={id} status={status} code={response.status_code}")
                logger.error(f"响应内容：{response.text}")
        except requests.exceptions.RequestException as e:
            logger.error(f"updateFileStatus 请求错误: {e}")

    def clean_resume_text(self, text):
        # 删除连续的空格，只保留一个空格
        text = re.sub(r' +', ' ', text)
        # 删除连续的换行符，只保留一个换行
        text = re.sub(r'\n+', '\n', text)
        # 删除行首和行尾的空格
        text = '\n'.join([line.strip() for line in text.split('\n')])
        # 删除空行
        text = '\n'.join([line for line in text.split('\n') if line.strip() != ''])
        return text

    # 一键完善
    async def improveInfo(self, position, content):
        output_improve_info = PydanticOutputParser(pydantic_object=ContentInfo)
        template = SystemMessagePromptTemplate.from_template(
            """
            你是一位经验丰富的人力资源管理专家，对面试流程和评估标准有着深入的理解，擅长从多个维度对候选人的表现进行客观、全面的分析，并能够根据组织的需求和发展战略，做出科学合理的决策。
            背景:
                用户在完成一系列面试后，需要对面试结果进行打分和综合评价，这可能是为了决定候选人的录用与否，或者是为了总结面试过程中的经验教训，以便优化未来的招聘流程。
            技能:
                你具备敏锐的观察力、出色的分析能力和扎实的人力资源管理知识，能够运用多种评估工具和方法，对候选人的专业技能、沟通能力、团队合作精神、问题解决能力、职业素养等进行全面评估，并能够结合组织的文化和价值观，对候选人进行综合考量。
            约束：
                - 评价应基于客观事实和明确的评估标准，避免主观偏见和情感因素的干扰。
                - 评价结果应具有可操作性，能够为下一步的决策提供明确的指导。
                - 如果评价内容尚未完成，则在评价基础进行补充。
            输出格式：{format_instructions}
            /no_think
            """
        )
        pass
