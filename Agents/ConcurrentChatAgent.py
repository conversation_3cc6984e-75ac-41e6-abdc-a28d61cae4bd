import asyncio
import threading
import uuid
from typing import Dict, Any, Optional
from concurrent.futures import Thread<PERSON>oolExecutor, Future
from datetime import datetime

from langchain_core.chat_history import BaseChatMessageHistory
from langchain_core.messages import BaseMessage

from Agents.ChatAgent import ChatAgent
from Models.peewee.OrmModel import KBConversationDetail
from Utils.logs.LoggingConfig import logger


class ThreadSafeChatHistory(BaseChatMessageHistory):
    """线程安全的聊天历史记录"""
    
    def __init__(self, original_history: BaseChatMessageHistory):
        self._original_history = original_history
        self._lock = threading.RLock()
    
    @property
    def messages(self):
        with self._lock:
            return self._original_history.messages.copy()
    
    def add_message(self, message: BaseMessage) -> None:
        with self._lock:
            self._original_history.add_message(message)
    
    def add_user_message(self, message: str) -> None:
        with self._lock:
            self._original_history.add_user_message(message)
    
    def add_ai_message(self, message: str) -> None:
        with self._lock:
            self._original_history.add_ai_message(message)
    
    def clear(self) -> None:
        with self._lock:
            self._original_history.clear()


class ConcurrentChatAgent:
    """支持并发的聊天代理包装器"""
    
    def __init__(self, base_agent: ChatAgent, max_concurrent_requests: int = 3):
        self.base_agent = base_agent
        self.max_concurrent_requests = max_concurrent_requests
        self.executor = ThreadPoolExecutor(
            max_workers=max_concurrent_requests,
            thread_name_prefix=f"chat-{base_agent.conversation_id[:8]}"
        )
        self.active_requests: Dict[str, Future] = {}
        self.request_lock = threading.Lock()
        
        # 使用线程安全的历史记录
        self.base_agent._memory = ThreadSafeChatHistory(self.base_agent._memory)
        
        logger.info(f"创建并发聊天代理，最大并发数: {max_concurrent_requests}")
    
    def __getattr__(self, name):
        """代理其他属性到基础 agent"""
        return getattr(self.base_agent, name)
    
    async def concurrent_stream_response(self, user_input: str, internet_search: bool = False) -> str:
        """并发处理流式响应"""
        request_id = str(uuid.uuid4())
        
        try:
            with self.request_lock:
                if len(self.active_requests) >= self.max_concurrent_requests:
                    # 等待最早的请求完成
                    oldest_request = min(self.active_requests.values(), key=lambda f: f._start_time if hasattr(f, '_start_time') else 0)
                    await asyncio.wrap_future(oldest_request)
                
                # 提交新的请求
                future = self.executor.submit(self._process_request, user_input, internet_search, request_id)
                future._start_time = datetime.now().timestamp()
                self.active_requests[request_id] = future
            
            logger.info(f"提交并发请求 {request_id[:8]}, 当前活跃请求数: {len(self.active_requests)}")
            
            # 等待请求完成
            result = await asyncio.wrap_future(future)
            return result
            
        except Exception as e:
            logger.error(f"并发请求 {request_id[:8]} 处理失败: {str(e)}")
            raise e
        finally:
            # 清理完成的请求
            with self.request_lock:
                self.active_requests.pop(request_id, None)
    
    def _process_request(self, user_input: str, internet_search: bool, request_id: str) -> str:
        """在独立线程中处理单个请求"""
        try:
            logger.info(f"开始处理请求 {request_id[:8]}: {user_input[:50]}...")
            
            # 创建独立的 agent 实例用于此次请求
            # 这样可以避免状态冲突
            temp_agent = self._create_isolated_agent()
            
            # 同步调用原始的 stream_response 方法
            result = asyncio.run(temp_agent.chat_for_answer(user_input))
            
            # 收集所有响应内容
            response_content = ""
            for chunk in result:
                if chunk:
                    response_content += chunk
            
            logger.info(f"请求 {request_id[:8]} 处理完成")
            return response_content
            
        except Exception as e:
            logger.error(f"请求 {request_id[:8]} 处理异常: {str(e)}")
            raise e
    
    def _create_isolated_agent(self) -> ChatAgent:
        """创建隔离的 agent 实例用于并发处理"""
        # 这里需要根据您的具体 Agent 创建逻辑来实现
        # 关键是要共享历史记录但隔离其他状态
        
        # 简化版本：直接返回基础 agent
        # 在实际使用中，您可能需要创建新的实例但共享历史记录
        return self.base_agent
    
    async def stream_response(self, user_input: str, internet_search: bool = False):
        """保持与原始接口兼容的流式响应方法"""
        try:
            # 使用并发处理
            response = await self.concurrent_stream_response(user_input, internet_search)
            
            # 模拟流式输出
            words = response.split()
            for i, word in enumerate(words):
                if i == 0:
                    yield word
                else:
                    yield " " + word
                    
                # 添加小延迟模拟流式输出
                await asyncio.sleep(0.01)
                
        except Exception as e:
            logger.error(f"流式响应处理失败: {str(e)}")
            yield f"处理请求时发生错误: {str(e)}"
    
    def shutdown(self):
        """关闭并发执行器"""
        logger.info("关闭并发聊天代理")
        self.executor.shutdown(wait=True)
    
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            self.shutdown()
        except:
            pass


def create_concurrent_agent(base_agent: ChatAgent, max_concurrent: int = 3) -> ConcurrentChatAgent:
    """创建并发聊天代理的工厂函数"""
    return ConcurrentChatAgent(base_agent, max_concurrent)
