import time
import threading
import asyncio
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
import statistics
from Utils.logs.LoggingConfig import logger


@dataclass
class RequestMetrics:
    """请求指标数据类"""
    start_time: float
    end_time: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    task_type: Optional[str] = None
    
    @property
    def duration(self) -> float:
        """请求持续时间"""
        if self.end_time:
            return self.end_time - self.start_time
        return time.time() - self.start_time


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self._requests: Dict[str, RequestMetrics] = {}
        self._completed_requests: deque = deque(maxlen=max_history)
        self._lock = threading.Lock()
        self._start_time = time.time()
        
        # 统计数据
        self._stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'concurrent_requests': 0,
            'max_concurrent': 0,
        }
        
        # 按任务类型分组的统计
        self._task_stats = defaultdict(lambda: {
            'count': 0,
            'success_count': 0,
            'total_duration': 0.0,
            'min_duration': float('inf'),
            'max_duration': 0.0,
        })
    
    def start_request(self, request_id: str, task_type: str = None) -> str:
        """开始监控一个请求"""
        with self._lock:
            metrics = RequestMetrics(
                start_time=time.time(),
                task_type=task_type
            )
            self._requests[request_id] = metrics
            self._stats['total_requests'] += 1
            self._stats['concurrent_requests'] += 1
            self._stats['max_concurrent'] = max(
                self._stats['max_concurrent'],
                self._stats['concurrent_requests']
            )
            
        logger.debug(f"开始监控请求: {request_id}, 类型: {task_type}")
        return request_id
    
    def end_request(self, request_id: str, success: bool = True, error_message: str = None):
        """结束监控一个请求"""
        with self._lock:
            if request_id not in self._requests:
                logger.warning(f"未找到请求ID: {request_id}")
                return
            
            metrics = self._requests[request_id]
            metrics.end_time = time.time()
            metrics.success = success
            metrics.error_message = error_message
            
            # 更新统计
            self._stats['concurrent_requests'] -= 1
            if success:
                self._stats['successful_requests'] += 1
            else:
                self._stats['failed_requests'] += 1
            
            # 更新任务类型统计
            if metrics.task_type:
                task_stat = self._task_stats[metrics.task_type]
                task_stat['count'] += 1
                if success:
                    task_stat['success_count'] += 1
                
                duration = metrics.duration
                task_stat['total_duration'] += duration
                task_stat['min_duration'] = min(task_stat['min_duration'], duration)
                task_stat['max_duration'] = max(task_stat['max_duration'], duration)
            
            # 移动到已完成队列
            self._completed_requests.append(metrics)
            del self._requests[request_id]
            
        logger.debug(f"结束监控请求: {request_id}, 成功: {success}, 耗时: {metrics.duration:.2f}秒")
    
    def get_current_stats(self) -> Dict:
        """获取当前统计信息"""
        with self._lock:
            # 计算响应时间统计
            durations = [req.duration for req in self._completed_requests if req.end_time]
            
            response_time_stats = {}
            if durations:
                response_time_stats = {
                    'avg': statistics.mean(durations),
                    'median': statistics.median(durations),
                    'min': min(durations),
                    'max': max(durations),
                    'p95': self._percentile(durations, 95),
                    'p99': self._percentile(durations, 99),
                }
            
            # 计算错误率
            total_completed = len(self._completed_requests)
            error_rate = 0.0
            if total_completed > 0:
                failed_count = sum(1 for req in self._completed_requests if not req.success)
                error_rate = (failed_count / total_completed) * 100
            
            # 计算吞吐量
            uptime = time.time() - self._start_time
            throughput = total_completed / uptime if uptime > 0 else 0
            
            return {
                'uptime_seconds': uptime,
                'total_requests': self._stats['total_requests'],
                'completed_requests': total_completed,
                'successful_requests': self._stats['successful_requests'],
                'failed_requests': self._stats['failed_requests'],
                'concurrent_requests': self._stats['concurrent_requests'],
                'max_concurrent': self._stats['max_concurrent'],
                'error_rate_percent': error_rate,
                'throughput_per_second': throughput,
                'response_time': response_time_stats,
                'task_stats': dict(self._task_stats),
            }
    
    def get_task_performance(self, task_type: str) -> Dict:
        """获取特定任务类型的性能统计"""
        with self._lock:
            if task_type not in self._task_stats:
                return {}
            
            stat = self._task_stats[task_type]
            result = stat.copy()
            
            if stat['count'] > 0:
                result['avg_duration'] = stat['total_duration'] / stat['count']
                result['success_rate'] = (stat['success_count'] / stat['count']) * 100
            else:
                result['avg_duration'] = 0.0
                result['success_rate'] = 0.0
            
            return result
    
    def log_performance_summary(self):
        """记录性能摘要"""
        stats = self.get_current_stats()
        
        logger.info("=== 性能监控摘要 ===")
        logger.info(f"运行时间: {stats['uptime_seconds']:.1f}秒")
        logger.info(f"总请求数: {stats['total_requests']}")
        logger.info(f"已完成: {stats['completed_requests']}")
        logger.info(f"成功: {stats['successful_requests']}")
        logger.info(f"失败: {stats['failed_requests']}")
        logger.info(f"当前并发: {stats['concurrent_requests']}")
        logger.info(f"最大并发: {stats['max_concurrent']}")
        logger.info(f"错误率: {stats['error_rate_percent']:.2f}%")
        logger.info(f"吞吐量: {stats['throughput_per_second']:.2f} 请求/秒")
        
        if stats['response_time']:
            rt = stats['response_time']
            logger.info(f"响应时间 - 平均: {rt['avg']:.2f}s, 中位数: {rt['median']:.2f}s")
            logger.info(f"响应时间 - P95: {rt['p95']:.2f}s, P99: {rt['p99']:.2f}s")
        
        # 按任务类型统计
        for task_type, task_stat in stats['task_stats'].items():
            if task_stat['count'] > 0:
                avg_duration = task_stat['total_duration'] / task_stat['count']
                success_rate = (task_stat['success_count'] / task_stat['count']) * 100
                logger.info(f"{task_type} - 数量: {task_stat['count']}, "
                           f"平均耗时: {avg_duration:.2f}s, 成功率: {success_rate:.1f}%")
    
    @staticmethod
    def _percentile(data: List[float], percentile: float) -> float:
        """计算百分位数"""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = (percentile / 100) * (len(sorted_data) - 1)
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


class RequestTracker:
    """请求跟踪器上下文管理器"""
    
    def __init__(self, request_id: str, task_type: str = None):
        self.request_id = request_id
        self.task_type = task_type
    
    def __enter__(self):
        performance_monitor.start_request(self.request_id, self.task_type)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        success = exc_type is None
        error_message = str(exc_val) if exc_val else None
        performance_monitor.end_request(self.request_id, success, error_message)


# 装饰器版本
def monitor_performance(task_type: str = None):
    """性能监控装饰器"""
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                request_id = f"{func.__name__}_{int(time.time() * 1000000)}"
                with RequestTracker(request_id, task_type):
                    return await func(*args, **kwargs)
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                request_id = f"{func.__name__}_{int(time.time() * 1000000)}"
                with RequestTracker(request_id, task_type):
                    return func(*args, **kwargs)
            return sync_wrapper
    return decorator
