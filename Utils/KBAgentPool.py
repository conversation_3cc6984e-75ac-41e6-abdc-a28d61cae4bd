import threading
import time
from typing import Dict, Callable

from Agents.ChatAgent import ChatAgent


class KBAgentPool:
    def __init__(self, max_agents=10):
        self.max_agents = max_agents
        self.agents: Dict[str, dict] = {}
        self.lock = threading.Lock()

    def get_agent(self, conversation_id: str) -> ChatAgent | None:
        if conversation_id in self.agents:
            agent_store_dict = self.agents[conversation_id]
            return agent_store_dict['agent']
        return None

    def try_get_agent(self, conversation_id: str, kb_id: int == None,
                      create_callback: Callable[[str], ChatAgent]) -> ChatAgent:
        """
         尝试获取与 conversation_id 关联的 agent。
         如果 agent 不存在，则通过 create_callback 创建一个新的 agent 并存储。

         Args:
             conversation_id (str): 会话的唯一标识符。
             create_callback (Callable[[str], ChatAgent]): 一个回调函数，用于创建新的 agent。

         Returns:
             ChatAgent: 与 conversation_id 关联的 agent。
         """

        current_time = time.time()
        if conversation_id in self.agents:
            agent_store_dict = self.agents[conversation_id]
            agent_store_dict['last_access'] = current_time
            agent_store_dict['is_active'] = True
            if (agent_store_dict['kb_id'] is not None and kb_id is not None and agent_store_dict['kb_id'] == kb_id) or (
                    agent_store_dict['kb_id'] is None and kb_id is None):
                return agent_store_dict['agent']
            else:
                self.remove_agent(conversation_id)
        with self.lock:
            if len(self.agents) >= self.max_agents:
                self.__cleanup_inactive_agents()

            new_agent = create_callback(conversation_id)
            self.agents[conversation_id] = {
                'agent': new_agent,
                'last_access': current_time,
                'is_active': True,
                'kb_id': kb_id
            }

        return new_agent

    def remove_agent(self, conversation_id: str) -> bool:
        """
        从 self.agents 中移除指定的 conversation_id 对应的 agent。

        Args:
            conversation_id (str): 要移除的会话的唯一标识符。

        Returns:
            bool: 如果成功移除返回 True，如果 conversation_id 不存在则返回 False。
        """
        with self.lock:
            if conversation_id in self.agents:
                # 如果 conversation_id 存在，移除对应的 agent
                del self.agents[conversation_id]
                return True
            else:
                # 如果 conversation_id 不存在，返回 False
                return False

    def __cleanup_inactive_agents(self) -> None:
        """清理超过30分钟未访问的代理"""
        current_time = time.time()
        inactive_timeout = 30 * 60  # 30分钟

        for conv_id, data in list(self.agents.items()):
            if (current_time - data['last_access'] > inactive_timeout and
                    not data['is_active']):
                del self.agents[conv_id]


# 创建全局的 agent pool 实例
kb_agent_pool = KBAgentPool(50)
