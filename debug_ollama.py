#!/usr/bin/env python3
"""
调试Ollama连接问题的脚本
"""

import asyncio
import sys
import os
import requests
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Utils.logs.LoggingConfig import logger

async def test_direct_ollama_call():
    """直接测试Ollama API调用"""
    try:
        logger.info("直接测试Ollama API调用...")
        
        # 获取模型配置
        from Services.SqlServer.KBModelsService import KBModelsService
        default_model = KBModelsService.get_generate_model()
        
        if not default_model:
            logger.error("❌ 无法获取默认模型配置")
            return False
        
        base_url = default_model.api_url
        model_name = default_model.model_name
        
        logger.info(f"模型配置 - URL: {base_url}, Model: {model_name}")
        
        # 直接调用Ollama API
        url = f"{base_url}/api/generate"
        payload = {
            "model": model_name,
            "prompt": "请简单回答：你好",
            "stream": False
        }
        
        logger.info(f"发送请求到: {url}")
        logger.info(f"请求数据: {json.dumps(payload, ensure_ascii=False)}")
        
        response = requests.post(url, json=payload, timeout=30)
        
        logger.info(f"响应状态码: {response.status_code}")
        logger.info(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            logger.info(f"✅ 直接API调用成功")
            logger.info(f"响应内容: {result.get('response', '')[:100]}")
            return True
        else:
            logger.error(f"❌ API调用失败")
            logger.error(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 直接API调用异常: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_langchain_ollama():
    """测试LangChain的Ollama调用"""
    try:
        logger.info("测试LangChain的Ollama调用...")
        
        from langchain_ollama import OllamaLLM
        from Services.SqlServer.KBModelsService import KBModelsService
        
        default_model = KBModelsService.get_generate_model()
        
        # 创建LangChain的OllamaLLM对象
        llm = OllamaLLM(
            base_url=default_model.api_url,
            model=default_model.model_name,
            temperature=0.3,
            num_ctx=1024,  # 减少上下文长度
            timeout=60
        )
        
        logger.info("开始LangChain调用...")
        result = llm.invoke("你好")
        logger.info(f"✅ LangChain调用成功，结果: {result[:100] if len(str(result)) > 100 else result}")
        return True
        
    except Exception as e:
        logger.error(f"❌ LangChain调用失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_model_availability():
    """测试模型可用性"""
    try:
        logger.info("测试模型可用性...")
        
        from Services.SqlServer.KBModelsService import KBModelsService
        default_model = KBModelsService.get_generate_model()
        
        base_url = default_model.api_url
        model_name = default_model.model_name
        
        # 检查模型列表
        url = f"{base_url}/api/tags"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            models = [model['name'] for model in data.get('models', [])]
            logger.info(f"可用模型: {models}")
            
            if model_name in models:
                logger.info(f"✅ 目标模型 '{model_name}' 可用")
                
                # 尝试加载模型
                load_url = f"{base_url}/api/generate"
                load_payload = {
                    "model": model_name,
                    "prompt": "",
                    "stream": False,
                    "keep_alive": "5m"
                }
                
                logger.info("尝试预加载模型...")
                load_response = requests.post(load_url, json=load_payload, timeout=30)
                logger.info(f"预加载响应状态: {load_response.status_code}")
                
                if load_response.status_code != 200:
                    logger.error(f"预加载失败: {load_response.text}")
                    return False
                
                return True
            else:
                logger.error(f"❌ 目标模型 '{model_name}' 不在可用列表中")
                return False
        else:
            logger.error(f"❌ 无法获取模型列表: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 模型可用性测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主测试函数"""
    logger.info("开始Ollama调试")
    
    # 测试模型可用性
    if not await test_model_availability():
        logger.error("模型可用性测试失败")
        return
    
    logger.info("等待2秒...")
    await asyncio.sleep(2)
    
    # 测试直接API调用
    if not await test_direct_ollama_call():
        logger.error("直接API调用失败")
        return
    
    logger.info("等待2秒...")
    await asyncio.sleep(2)
    
    # 测试LangChain调用
    if not await test_langchain_ollama():
        logger.error("LangChain调用失败")
        return
    
    logger.info("✅ 所有调试测试通过")

if __name__ == "__main__":
    asyncio.run(main())
