"""
并发配置文件
用于配置Ollama和其他LLM的并发参数
"""

# Ollama并发配置
OLLAMA_CONCURRENT_CONFIG = {
    # 最大并发请求数
    "max_concurrent_requests": 8,
    
    # HTTP连接池配置
    "connection_pool": {
        "total_connections": 20,  # 总连接数
        "connections_per_host": 10,  # 每个主机连接数
        "keepalive_timeout": 60,  # 保持连接时间(秒)
        "connect_timeout": 30,  # 连接超时(秒)
        "read_timeout": 120,  # 读取超时(秒)
        "total_timeout": 180,  # 总超时(秒)
    },
    
    # LLM模型配置
    "model_config": {
        "timeout": 120,  # 模型调用超时(秒)
        "keep_alive": "10m",  # 模型保持在内存中的时间
        "num_retries": 3,  # 重试次数
        "retry_delay": 1.0,  # 重试延迟(秒)
    },
    
    # 线程池配置
    "thread_pool": {
        "max_workers": 8,  # 最大工作线程数
        "thread_name_prefix": "ollama-worker",
    },
    
    # 缓存配置
    "cache": {
        "enable_llm_cache": True,  # 是否启用LLM对象缓存
        "cache_ttl": 3600,  # 缓存生存时间(秒)
        "max_cache_size": 50,  # 最大缓存数量
    }
}

# 简历处理并发配置
RESUME_PROCESSING_CONFIG = {
    # 并发处理的任务类型
    "concurrent_tasks": [
        "basic_info",      # 基本信息提取
        "personal_info",   # 个人经历提取
        "project_info",    # 项目经验提取
        "score_info"       # 评分计算
    ],
    
    # 每种任务的优先级（数字越小优先级越高）
    "task_priority": {
        "basic_info": 1,
        "personal_info": 2,
        "project_info": 3,
        "score_info": 4
    },
    
    # 任务超时配置
    "task_timeout": {
        "basic_info": 60,      # 基本信息提取超时(秒)
        "personal_info": 90,   # 个人经历提取超时(秒)
        "project_info": 120,   # 项目经验提取超时(秒)
        "score_info": 60       # 评分计算超时(秒)
    },
    
    # 重试配置
    "retry_config": {
        "max_retries": 2,      # 最大重试次数
        "retry_delay": 2.0,    # 重试延迟(秒)
        "exponential_backoff": True,  # 是否使用指数退避
    }
}

# 性能监控配置
PERFORMANCE_CONFIG = {
    "enable_monitoring": True,  # 是否启用性能监控
    "log_slow_requests": True,  # 是否记录慢请求
    "slow_request_threshold": 30.0,  # 慢请求阈值(秒)
    "metrics_collection": {
        "request_count": True,      # 统计请求数量
        "response_time": True,      # 统计响应时间
        "error_rate": True,         # 统计错误率
        "concurrent_requests": True, # 统计并发请求数
    }
}

# 根据系统资源动态调整配置
def get_optimized_config():
    """
    根据系统资源动态调整并发配置
    """
    import psutil
    import os
    
    # 获取系统信息
    cpu_count = os.cpu_count()
    memory_gb = psutil.virtual_memory().total / (1024**3)
    
    # 基础配置
    config = OLLAMA_CONCURRENT_CONFIG.copy()
    
    # 根据CPU核心数调整并发数
    if cpu_count >= 8:
        config["max_concurrent_requests"] = min(12, cpu_count)
        config["thread_pool"]["max_workers"] = min(12, cpu_count)
    elif cpu_count >= 4:
        config["max_concurrent_requests"] = min(8, cpu_count)
        config["thread_pool"]["max_workers"] = min(8, cpu_count)
    else:
        config["max_concurrent_requests"] = max(2, cpu_count)
        config["thread_pool"]["max_workers"] = max(2, cpu_count)
    
    # 根据内存调整连接池大小
    if memory_gb >= 16:
        config["connection_pool"]["total_connections"] = 30
        config["connection_pool"]["connections_per_host"] = 15
    elif memory_gb >= 8:
        config["connection_pool"]["total_connections"] = 20
        config["connection_pool"]["connections_per_host"] = 10
    else:
        config["connection_pool"]["total_connections"] = 10
        config["connection_pool"]["connections_per_host"] = 5
    
    return config

# 获取当前推荐配置
CURRENT_CONFIG = get_optimized_config()
