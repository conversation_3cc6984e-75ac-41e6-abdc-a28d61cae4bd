#!/usr/bin/env python3
"""
快速测试脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Utils.logs.LoggingConfig import logger

# 简单的简历内容
SIMPLE_RESUME = """
姓名：李四
性别：女
年龄：25
学历：硕士
邮箱：<EMAIL>
手机：13900139000
"""

async def test_simple_processing():
    """测试简单处理"""
    try:
        logger.info("开始简单测试...")
        
        from Agents.TalentAgent import TalentAgent
        
        agent = TalentAgent()
        logger.info("TalentAgent创建成功")
        
        # 测试简历处理
        logger.info("开始处理简历...")
        result = await agent._formatting(SIMPLE_RESUME)
        
        if result:
            logger.info("✅ 处理成功!")
            logger.info(f"姓名: {result.userName}")
            logger.info(f"年龄: {result.age}")
            logger.info(f"性别: {result.sex}")
            logger.info(f"学历: {result.education}")
            return True
        else:
            logger.error("❌ 处理失败，返回None")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主函数"""
    logger.info("开始快速测试")
    
    success = await test_simple_processing()
    
    if success:
        logger.info("🎉 测试通过!")
    else:
        logger.error("💥 测试失败!")

if __name__ == "__main__":
    asyncio.run(main())
