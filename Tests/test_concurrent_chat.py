#!/usr/bin/env python3
"""
并发聊天测试脚本
用于测试多轮会话的并发处理能力
"""

import asyncio
import aiohttp
import time
import json
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor
import threading


class ConcurrentChatTester:
    """并发聊天测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.chat_endpoint = f"{base_url}/agentService/api/knowledge/chat"
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def send_chat_request(self, conversation_id: str, question: str, 
                               kb_id: int = None, model_id: int = None) -> Dict[str, Any]:
        """发送单个聊天请求"""
        payload = {
            "conversation_id": conversation_id,
            "question": question,
            "kb_id": kb_id,
            "model_id": model_id,
            "internet_search": False
        }
        
        start_time = time.time()
        try:
            async with self.session.post(self.chat_endpoint, json=payload) as response:
                end_time = time.time()
                
                if response.status == 200:
                    # 处理流式响应
                    content = ""
                    async for line in response.content:
                        if line:
                            content += line.decode('utf-8')
                    
                    return {
                        "success": True,
                        "conversation_id": conversation_id,
                        "question": question,
                        "response": content,
                        "duration": end_time - start_time,
                        "status_code": response.status
                    }
                else:
                    return {
                        "success": False,
                        "conversation_id": conversation_id,
                        "question": question,
                        "error": f"HTTP {response.status}",
                        "duration": end_time - start_time,
                        "status_code": response.status
                    }
        except Exception as e:
            end_time = time.time()
            return {
                "success": False,
                "conversation_id": conversation_id,
                "question": question,
                "error": str(e),
                "duration": end_time - start_time,
                "status_code": None
            }
    
    async def test_single_conversation_concurrent(self, conversation_id: str, 
                                                 questions: List[str], 
                                                 kb_id: int = None) -> List[Dict[str, Any]]:
        """测试单个会话的并发处理"""
        print(f"测试会话 {conversation_id} 的并发处理，{len(questions)} 个问题")
        
        # 创建并发任务
        tasks = []
        for i, question in enumerate(questions):
            task = asyncio.create_task(
                self.send_chat_request(conversation_id, f"[{i+1}] {question}", kb_id)
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append({
                    "success": False,
                    "error": str(result),
                    "duration": 0
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def test_multiple_conversations(self, num_conversations: int, 
                                        questions_per_conversation: int = 3) -> Dict[str, Any]:
        """测试多个会话的并发处理"""
        print(f"测试 {num_conversations} 个会话，每个会话 {questions_per_conversation} 个问题")
        
        # 准备测试问题
        test_questions = [
            "你好，请介绍一下自己",
            "今天天气怎么样？",
            "请解释一下人工智能的基本概念",
            "什么是机器学习？",
            "深度学习和机器学习有什么区别？",
            "请推荐一些学习编程的资源",
            "Python 和 Java 哪个更适合初学者？",
            "如何提高编程技能？"
        ]
        
        # 创建会话任务
        conversation_tasks = []
        for i in range(num_conversations):
            conversation_id = f"test_conv_{i}_{int(time.time())}"
            questions = test_questions[:questions_per_conversation]
            
            task = asyncio.create_task(
                self.test_single_conversation_concurrent(conversation_id, questions)
            )
            conversation_tasks.append((conversation_id, task))
        
        # 等待所有会话完成
        start_time = time.time()
        all_results = {}
        
        for conversation_id, task in conversation_tasks:
            try:
                results = await task
                all_results[conversation_id] = results
            except Exception as e:
                all_results[conversation_id] = [{"success": False, "error": str(e)}]
        
        end_time = time.time()
        
        # 统计结果
        total_requests = sum(len(results) for results in all_results.values())
        successful_requests = sum(
            len([r for r in results if r.get("success", False)]) 
            for results in all_results.values()
        )
        
        total_duration = end_time - start_time
        avg_response_time = sum(
            r.get("duration", 0) 
            for results in all_results.values() 
            for r in results if r.get("duration")
        ) / max(total_requests, 1)
        
        return {
            "summary": {
                "total_conversations": num_conversations,
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "failed_requests": total_requests - successful_requests,
                "success_rate": successful_requests / total_requests * 100,
                "total_duration": total_duration,
                "avg_response_time": avg_response_time,
                "requests_per_second": total_requests / total_duration
            },
            "detailed_results": all_results
        }
    
    def print_test_results(self, results: Dict[str, Any]):
        """打印测试结果"""
        summary = results["summary"]
        
        print("\n" + "=" * 60)
        print("并发测试结果汇总")
        print("=" * 60)
        print(f"总会话数: {summary['total_conversations']}")
        print(f"总请求数: {summary['total_requests']}")
        print(f"成功请求: {summary['successful_requests']}")
        print(f"失败请求: {summary['failed_requests']}")
        print(f"成功率: {summary['success_rate']:.2f}%")
        print(f"总耗时: {summary['total_duration']:.2f}s")
        print(f"平均响应时间: {summary['avg_response_time']:.2f}s")
        print(f"请求吞吐量: {summary['requests_per_second']:.2f} req/s")
        
        # 打印详细结果
        print("\n详细结果:")
        for conv_id, conv_results in results["detailed_results"].items():
            print(f"\n会话 {conv_id}:")
            for i, result in enumerate(conv_results):
                status = "✓" if result.get("success", False) else "✗"
                duration = result.get("duration", 0)
                question = result.get("question", "Unknown")[:50]
                print(f"  {status} [{i+1}] {question}... ({duration:.2f}s)")
    
    async def run_stress_test(self, max_concurrent_conversations: int = 10):
        """运行压力测试"""
        print("开始压力测试...")
        
        for num_conv in [1, 2, 4, 6, 8, max_concurrent_conversations]:
            print(f"\n测试 {num_conv} 个并发会话...")
            results = await self.test_multiple_conversations(num_conv, 2)
            
            summary = results["summary"]
            print(f"  成功率: {summary['success_rate']:.1f}%")
            print(f"  平均响应时间: {summary['avg_response_time']:.2f}s")
            print(f"  吞吐量: {summary['requests_per_second']:.2f} req/s")
            
            # 如果成功率低于 80%，停止测试
            if summary['success_rate'] < 80:
                print(f"  ⚠️  成功率过低，停止增加并发数")
                break


async def main():
    """主测试函数"""
    print("Ollama 并发聊天测试工具")
    print("=" * 50)
    
    # 检查服务器连接
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/docs") as response:
                if response.status == 200:
                    print("✓ 服务器连接正常")
                else:
                    print(f"✗ 服务器响应异常: {response.status}")
                    return
    except Exception as e:
        print(f"✗ 无法连接到服务器: {e}")
        print("请确保服务器正在运行在 http://localhost:8000")
        return
    
    async with ConcurrentChatTester() as tester:
        while True:
            print("\n请选择测试类型:")
            print("1. 单会话并发测试 (3个并发问题)")
            print("2. 多会话并发测试 (5个会话，每个2个问题)")
            print("3. 压力测试 (逐步增加并发数)")
            print("4. 自定义测试")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-4): ").strip()
            
            if choice == "0":
                break
            elif choice == "1":
                # 单会话并发测试
                questions = [
                    "你好，请介绍一下自己",
                    "什么是人工智能？",
                    "请推荐一些学习资源"
                ]
                conv_id = f"single_test_{int(time.time())}"
                results = await tester.test_single_conversation_concurrent(conv_id, questions)
                
                print(f"\n单会话并发测试结果:")
                for i, result in enumerate(results):
                    status = "✓" if result.get("success", False) else "✗"
                    duration = result.get("duration", 0)
                    print(f"  {status} 问题 {i+1}: {duration:.2f}s")
                
            elif choice == "2":
                # 多会话并发测试
                results = await tester.test_multiple_conversations(5, 2)
                tester.print_test_results(results)
                
            elif choice == "3":
                # 压力测试
                await tester.run_stress_test(10)
                
            elif choice == "4":
                # 自定义测试
                try:
                    num_conv = int(input("请输入会话数量: "))
                    num_questions = int(input("请输入每个会话的问题数量: "))
                    
                    results = await tester.test_multiple_conversations(num_conv, num_questions)
                    tester.print_test_results(results)
                except ValueError:
                    print("输入无效，请输入数字")
            else:
                print("无效选择，请重试")


if __name__ == "__main__":
    asyncio.run(main())
