#!/usr/bin/env python3
"""
简化的测试脚本，用于快速定位问题
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Utils.logs.LoggingConfig import logger

# 测试用的简历内容
SAMPLE_RESUME = """
姓名：张三
性别：男
年龄：28
学历：本科
"""

async def test_basic_import():
    """测试基本导入"""
    try:
        logger.info("测试基本导入...")
        from Agents.TalentAgentProcessing import TalentAgentProcessing
        logger.info("✅ TalentAgentProcessing 导入成功")
        
        processor = TalentAgentProcessing()
        logger.info("✅ TalentAgentProcessing 实例化成功")
        
        return processor
    except Exception as e:
        logger.error(f"❌ 导入失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return None

async def test_llm_helper():
    """测试LLM Helper"""
    try:
        logger.info("测试LLM Helper...")
        from LLM.LLMManager import sys_llm_manager
        
        llm_helper = sys_llm_manager.get_generate_use_llm_helper()
        if llm_helper is None:
            logger.error("❌ 无法获取LLM Helper")
            return False
        
        logger.info(f"✅ LLM Helper获取成功: {type(llm_helper).__name__}")
        
        # 测试获取LLM对象
        llm_obj = llm_helper.get_llm_object(0.3, 6144)
        logger.info(f"✅ LLM对象创建成功: {type(llm_obj).__name__}")
        
        return True
    except Exception as e:
        logger.error(f"❌ LLM Helper测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_simple_llm_call():
    """测试简单的LLM调用"""
    try:
        logger.info("测试简单的LLM调用...")
        from LLM.LLMManager import sys_llm_manager
        
        llm_helper = sys_llm_manager.get_generate_use_llm_helper()
        if llm_helper is None:
            logger.error("❌ 无法获取LLM Helper")
            return False
        
        llm_obj = llm_helper.get_llm_object(0.3, 6144)
        
        # 简单的调用测试
        logger.info("开始LLM调用...")
        result = llm_obj.invoke("请简单回答：你好")
        logger.info(f"✅ LLM调用成功，结果: {result[:100] if len(str(result)) > 100 else result}")
        
        return True
    except Exception as e:
        logger.error(f"❌ LLM调用失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_parser():
    """测试解析器"""
    try:
        logger.info("测试解析器...")
        from langchain_core.output_parsers import PydanticOutputParser
        from Models.agent.TalentInfoProcessing import TalentAgentProcessing as TalentModel
        
        parser = PydanticOutputParser(pydantic_object=TalentModel)
        logger.info("✅ 解析器创建成功")
        
        # 测试格式指令
        format_instructions = parser.get_format_instructions()
        logger.info(f"✅ 格式指令获取成功，长度: {len(format_instructions)}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 解析器测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主测试函数"""
    logger.info("开始简化测试")
    
    # 测试基本导入
    processor = await test_basic_import()
    if processor is None:
        logger.error("基本导入失败，停止测试")
        return
    
    # 测试LLM Helper
    if not await test_llm_helper():
        logger.error("LLM Helper测试失败，停止测试")
        return
    
    # 测试解析器
    if not await test_parser():
        logger.error("解析器测试失败，停止测试")
        return
    
    # 测试简单LLM调用
    if not await test_simple_llm_call():
        logger.error("LLM调用测试失败")
        return
    
    logger.info("✅ 所有基础测试通过")

if __name__ == "__main__":
    asyncio.run(main())
