#!/usr/bin/env python3
"""
Ollama 并发优化脚本
用于配置 Ollama 服务器以支持更好的并发性能
"""

import os
import subprocess
import sys
from pathlib import Path


class OllamaOptimizer:
    """Ollama 并发优化器"""
    
    def __init__(self):
        self.recommended_env_vars = {
            "OLLAMA_NUM_PARALLEL": "4",  # 并行请求数
            "OLLAMA_MAX_LOADED_MODELS": "3",  # 最大加载模型数
            "OLLAMA_FLASH_ATTENTION": "1",  # 启用 Flash Attention
            "OLLAMA_HOST": "0.0.0.0:11434",  # 监听地址
            "OLLAMA_KEEP_ALIVE": "5m",  # 模型保持时间
            "OLLAMA_MAX_QUEUE": "512",  # 最大队列长度
            "OLLAMA_RUNNERS_DIR": "/tmp/ollama/runners",  # 运行器目录
            "OLLAMA_TMPDIR": "/tmp/ollama",  # 临时目录
        }
    
    def check_current_config(self):
        """检查当前 Ollama 配置"""
        print("=== 当前 Ollama 环境变量配置 ===")
        for var, recommended in self.recommended_env_vars.items():
            current = os.environ.get(var, "未设置")
            status = "✓" if current == recommended else "✗"
            print(f"{status} {var}: {current} (推荐: {recommended})")
        print()
    
    def apply_optimizations(self, permanent=False):
        """应用优化配置"""
        print("=== 应用 Ollama 并发优化配置 ===")
        
        if permanent:
            self._apply_permanent_config()
        else:
            self._apply_temporary_config()
    
    def _apply_temporary_config(self):
        """应用临时配置（当前会话）"""
        print("应用临时配置（仅当前会话有效）...")
        for var, value in self.recommended_env_vars.items():
            os.environ[var] = value
            print(f"设置 {var}={value}")
        print("临时配置已应用！")
    
    def _apply_permanent_config(self):
        """应用永久配置"""
        print("应用永久配置...")
        
        # 检测操作系统
        if sys.platform.startswith('win'):
            self._apply_windows_config()
        else:
            self._apply_unix_config()
    
    def _apply_windows_config(self):
        """Windows 系统配置"""
        print("检测到 Windows 系统，配置系统环境变量...")
        
        for var, value in self.recommended_env_vars.items():
            try:
                # 使用 setx 命令设置系统环境变量
                subprocess.run(['setx', var, value], check=True, capture_output=True)
                print(f"✓ 设置 {var}={value}")
            except subprocess.CalledProcessError as e:
                print(f"✗ 设置 {var} 失败: {e}")
        
        print("\n注意：Windows 环境变量更改需要重启终端或系统才能生效！")
    
    def _apply_unix_config(self):
        """Unix/Linux 系统配置"""
        print("检测到 Unix/Linux 系统，配置环境变量...")
        
        # 尝试写入到 ~/.bashrc 或 ~/.zshrc
        shell_configs = [
            Path.home() / ".bashrc",
            Path.home() / ".zshrc",
            Path.home() / ".profile"
        ]
        
        config_lines = []
        for var, value in self.recommended_env_vars.items():
            config_lines.append(f"export {var}={value}")
        
        for config_file in shell_configs:
            if config_file.exists():
                try:
                    with open(config_file, 'a') as f:
                        f.write("\n# Ollama 并发优化配置\n")
                        f.write("\n".join(config_lines))
                        f.write("\n")
                    print(f"✓ 配置已写入 {config_file}")
                    break
                except Exception as e:
                    print(f"✗ 写入 {config_file} 失败: {e}")
        
        print("\n注意：请运行 'source ~/.bashrc' 或重启终端使配置生效！")
    
    def create_systemd_service(self):
        """创建 systemd 服务文件（Linux）"""
        if not sys.platform.startswith('linux'):
            print("systemd 服务仅支持 Linux 系统")
            return
        
        service_content = f"""[Unit]
Description=Ollama Server
After=network-online.target

[Service]
ExecStart=/usr/local/bin/ollama serve
User=ollama
Group=ollama
Restart=always
RestartSec=3
{chr(10).join([f'Environment="{var}={value}"' for var, value in self.recommended_env_vars.items()])}

[Install]
WantedBy=default.target
"""
        
        service_file = Path("/etc/systemd/system/ollama.service")
        try:
            with open(service_file, 'w') as f:
                f.write(service_content)
            print(f"✓ systemd 服务文件已创建: {service_file}")
            print("运行以下命令启用服务:")
            print("  sudo systemctl daemon-reload")
            print("  sudo systemctl enable ollama")
            print("  sudo systemctl start ollama")
        except PermissionError:
            print("✗ 需要 root 权限创建 systemd 服务文件")
            print("请使用 sudo 运行此脚本")
    
    def test_ollama_connection(self):
        """测试 Ollama 连接"""
        print("=== 测试 Ollama 连接 ===")
        try:
            import requests
            
            # 测试基本连接
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                print("✓ Ollama 服务器连接正常")
                models = response.json().get('models', [])
                print(f"✓ 已加载模型数量: {len(models)}")
                for model in models[:3]:  # 显示前3个模型
                    print(f"  - {model.get('name', 'Unknown')}")
            else:
                print(f"✗ Ollama 服务器响应异常: {response.status_code}")
        except ImportError:
            print("✗ 需要安装 requests 库: pip install requests")
        except Exception as e:
            print(f"✗ 连接 Ollama 服务器失败: {e}")
    
    def benchmark_concurrency(self):
        """并发性能基准测试"""
        print("=== 并发性能基准测试 ===")
        try:
            import asyncio
            import aiohttp
            import time
            
            async def test_request(session, model_name="qwen2.5:7b"):
                """单个测试请求"""
                url = "http://localhost:11434/api/generate"
                data = {
                    "model": model_name,
                    "prompt": "Hello, how are you?",
                    "stream": False
                }
                
                try:
                    async with session.post(url, json=data) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            return None
                except Exception as e:
                    return None
            
            async def run_benchmark():
                """运行并发基准测试"""
                concurrent_requests = [1, 2, 4, 8]
                
                async with aiohttp.ClientSession() as session:
                    for num_requests in concurrent_requests:
                        print(f"\n测试 {num_requests} 个并发请求...")
                        
                        start_time = time.time()
                        tasks = [test_request(session) for _ in range(num_requests)]
                        results = await asyncio.gather(*tasks, return_exceptions=True)
                        end_time = time.time()
                        
                        successful = len([r for r in results if r is not None and not isinstance(r, Exception)])
                        total_time = end_time - start_time
                        
                        print(f"  成功请求: {successful}/{num_requests}")
                        print(f"  总耗时: {total_time:.2f}s")
                        print(f"  平均耗时: {total_time/num_requests:.2f}s")
                        print(f"  QPS: {successful/total_time:.2f}")
            
            asyncio.run(run_benchmark())
            
        except ImportError:
            print("✗ 需要安装 aiohttp 库: pip install aiohttp")
        except Exception as e:
            print(f"✗ 基准测试失败: {e}")
    
    def print_optimization_guide(self):
        """打印优化指南"""
        print("=== Ollama 并发优化指南 ===")
        print()
        print("1. 环境变量优化:")
        for var, value in self.recommended_env_vars.items():
            print(f"   export {var}={value}")
        print()
        print("2. 硬件要求:")
        print("   - GPU 内存: 建议至少 8GB 用于并发推理")
        print("   - 系统内存: 建议至少 16GB")
        print("   - CPU: 多核心处理器")
        print()
        print("3. 模型优化:")
        print("   - 使用较小的模型（如 7B 而非 14B）提高并发性能")
        print("   - 预加载常用模型到内存")
        print("   - 设置合适的 keep_alive 时间")
        print()
        print("4. 网络优化:")
        print("   - 使用连接池")
        print("   - 设置合适的超时时间")
        print("   - 考虑使用负载均衡")


def main():
    """主函数"""
    optimizer = OllamaOptimizer()
    
    print("Ollama 并发优化工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 检查当前配置")
        print("2. 应用临时优化配置")
        print("3. 应用永久优化配置")
        print("4. 创建 systemd 服务 (Linux)")
        print("5. 测试 Ollama 连接")
        print("6. 并发性能基准测试")
        print("7. 显示优化指南")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-7): ").strip()
        
        if choice == "0":
            break
        elif choice == "1":
            optimizer.check_current_config()
        elif choice == "2":
            optimizer.apply_optimizations(permanent=False)
        elif choice == "3":
            optimizer.apply_optimizations(permanent=True)
        elif choice == "4":
            optimizer.create_systemd_service()
        elif choice == "5":
            optimizer.test_ollama_connection()
        elif choice == "6":
            optimizer.benchmark_concurrency()
        elif choice == "7":
            optimizer.print_optimization_guide()
        else:
            print("无效选择，请重试")


if __name__ == "__main__":
    main()
