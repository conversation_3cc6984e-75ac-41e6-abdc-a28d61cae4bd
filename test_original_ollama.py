#!/usr/bin/env python3
"""
测试原始OllamaHelper的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Utils.logs.LoggingConfig import logger

async def test_original_ollama():
    """测试原始的OllamaHelper"""
    try:
        logger.info("测试原始OllamaHelper...")
        
        # 临时禁用并发模式
        from LLM.LLMManager import sys_llm_manager
        sys_llm_manager._use_concurrent_ollama = False
        
        llm_helper = sys_llm_manager.get_generate_use_llm_helper()
        if llm_helper is None:
            logger.error("❌ 无法获取LLM Helper")
            return False
        
        logger.info(f"✅ LLM Helper获取成功: {type(llm_helper).__name__}")
        
        # 测试获取LLM对象
        llm_obj = llm_helper.get_llm_object(0.3, 6144)
        logger.info(f"✅ LLM对象创建成功: {type(llm_obj).__name__}")
        
        # 简单的调用测试
        logger.info("开始LLM调用...")
        result = llm_obj.invoke("请简单回答：你好")
        logger.info(f"✅ LLM调用成功，结果: {result[:100] if len(str(result)) > 100 else result}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 原始OllamaHelper测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_concurrent_ollama():
    """测试并发OllamaHelper"""
    try:
        logger.info("测试并发OllamaHelper...")
        
        # 启用并发模式
        from LLM.LLMManager import sys_llm_manager
        sys_llm_manager._use_concurrent_ollama = True
        
        # 清除缓存
        sys_llm_manager._llm_map.clear()
        
        llm_helper = sys_llm_manager.get_generate_use_llm_helper()
        if llm_helper is None:
            logger.error("❌ 无法获取并发LLM Helper")
            return False
        
        logger.info(f"✅ 并发LLM Helper获取成功: {type(llm_helper).__name__}")
        
        # 测试获取LLM对象
        llm_obj = llm_helper.get_llm_object(0.3, 6144)
        logger.info(f"✅ 并发LLM对象创建成功: {type(llm_obj).__name__}")
        
        # 简单的调用测试
        logger.info("开始并发LLM调用...")
        result = llm_obj.invoke("请简单回答：你好")
        logger.info(f"✅ 并发LLM调用成功，结果: {result[:100] if len(str(result)) > 100 else result}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 并发OllamaHelper测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_simple_resume_processing():
    """测试简单的简历处理"""
    try:
        logger.info("测试简单的简历处理...")
        
        # 使用原始模式
        from LLM.LLMManager import sys_llm_manager
        sys_llm_manager._use_concurrent_ollama = False
        sys_llm_manager._llm_map.clear()
        
        from Agents.TalentAgentProcessing import TalentAgentProcessing
        
        processor = TalentAgentProcessing()
        
        # 简单的简历内容
        resume_content = """
        姓名：张三
        性别：男
        年龄：28
        学历：本科
        邮箱：<EMAIL>
        手机：13800138000
        """
        
        logger.info("开始处理简历...")
        result = await processor._formatting(resume_content)
        
        if result:
            logger.info(f"✅ 简历处理成功")
            logger.info(f"姓名: {result.userName}")
            logger.info(f"年龄: {result.age}")
            logger.info(f"性别: {result.sex}")
            return True
        else:
            logger.error("❌ 简历处理失败，返回None")
            return False
            
    except Exception as e:
        logger.error(f"❌ 简历处理测试失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主测试函数"""
    logger.info("开始原始Ollama测试")
    
    # 测试原始OllamaHelper
    if not await test_original_ollama():
        logger.error("原始OllamaHelper测试失败，停止测试")
        return
    
    logger.info("等待2秒...")
    await asyncio.sleep(2)
    
    # 测试并发OllamaHelper
    if not await test_concurrent_ollama():
        logger.error("并发OllamaHelper测试失败")
        return
    
    logger.info("等待2秒...")
    await asyncio.sleep(2)
    
    # 测试简单的简历处理
    if not await test_simple_resume_processing():
        logger.error("简历处理测试失败")
        return
    
    logger.info("✅ 所有测试通过")

if __name__ == "__main__":
    asyncio.run(main())
