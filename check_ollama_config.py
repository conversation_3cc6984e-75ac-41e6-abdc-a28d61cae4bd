#!/usr/bin/env python3
"""
检查Ollama配置和连接的脚本
"""

import asyncio
import aiohttp
import requests
import sys
import os
import json
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Configs.Config import SysConfig
from Configs.ConcurrencyConfig import CURRENT_CONFIG
from Utils.logs.LoggingConfig import logger


async def check_ollama_connection(base_url: str) -> Dict[str, Any]:
    """检查Ollama连接状态"""
    try:
        # 检查基本连接
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/api/tags", timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    models = data.get('models', [])
                    return {
                        'connected': True,
                        'models': [model['name'] for model in models],
                        'model_count': len(models)
                    }
                else:
                    return {
                        'connected': False,
                        'error': f"HTTP {response.status}"
                    }
    except Exception as e:
        return {
            'connected': False,
            'error': str(e)
        }


async def test_concurrent_requests(base_url: str, model_name: str, num_requests: int = 3) -> Dict[str, Any]:
    """测试并发请求"""
    if not base_url or not model_name:
        return {'error': 'Missing base_url or model_name'}
    
    async def single_request(session: aiohttp.ClientSession, request_id: int):
        try:
            payload = {
                'model': model_name,
                'prompt': f'Hello, this is test request {request_id}. Please respond briefly.',
                'stream': False
            }
            
            start_time = asyncio.get_event_loop().time()
            async with session.post(f"{base_url}/api/generate", json=payload, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    end_time = asyncio.get_event_loop().time()
                    return {
                        'request_id': request_id,
                        'success': True,
                        'duration': end_time - start_time,
                        'response_length': len(data.get('response', ''))
                    }
                else:
                    return {
                        'request_id': request_id,
                        'success': False,
                        'error': f"HTTP {response.status}"
                    }
        except Exception as e:
            return {
                'request_id': request_id,
                'success': False,
                'error': str(e)
            }
    
    try:
        connector = aiohttp.TCPConnector(limit=num_requests)
        async with aiohttp.ClientSession(connector=connector) as session:
            # 并发发送请求
            tasks = [single_request(session, i) for i in range(num_requests)]
            start_time = asyncio.get_event_loop().time()
            results = await asyncio.gather(*tasks)
            end_time = asyncio.get_event_loop().time()
            
            # 统计结果
            successful = [r for r in results if r.get('success')]
            failed = [r for r in results if not r.get('success')]
            
            return {
                'total_time': end_time - start_time,
                'total_requests': num_requests,
                'successful_requests': len(successful),
                'failed_requests': len(failed),
                'average_duration': sum(r['duration'] for r in successful) / len(successful) if successful else 0,
                'results': results
            }
    except Exception as e:
        return {'error': str(e)}


def check_system_config():
    """检查系统配置"""
    logger.info("=== 系统配置检查 ===")
    
    try:
        # 检查基本配置
        logger.info(f"当前并发配置: {CURRENT_CONFIG}")
        
        # 检查LLM配置
        if 'agents' in SysConfig and 'kb_agent' in SysConfig['agents']:
            agent_config = SysConfig['agents']['kb_agent']
            logger.info(f"Agent温度: {agent_config.get('temperature', 'N/A')}")
        
        if 'talent' in SysConfig:
            talent_config = SysConfig['talent']
            logger.info(f"Talent URL: {talent_config.get('talent_url', 'N/A')}")
            logger.info(f"Tokens: {talent_config.get('tokens', 'N/A')}")
        
        return True
    except Exception as e:
        logger.error(f"配置检查失败: {str(e)}")
        return False


async def main():
    """主检查函数"""
    logger.info("开始Ollama配置检查")
    
    # 检查系统配置
    config_ok = check_system_config()
    if not config_ok:
        logger.error("系统配置检查失败")
        return
    
    # 尝试从配置中获取Ollama信息
    ollama_url = None
    model_name = None
    
    try:
        # 这里需要根据您的实际配置结构调整
        from Services.SqlServer.KBModelsService import KBModelsService
        default_model = KBModelsService.get_generate_model()
        if default_model:
            ollama_url = default_model.api_url
            model_name = default_model.model_name
            logger.info(f"从数据库获取模型配置 - URL: {ollama_url}, Model: {model_name}")
    except Exception as e:
        logger.warning(f"无法从数据库获取模型配置: {str(e)}")
        # 使用默认配置
        ollama_url = "http://localhost:11434"
        model_name = "llama2"  # 请根据您的实际模型名称修改
        logger.info(f"使用默认配置 - URL: {ollama_url}, Model: {model_name}")
    
    if not ollama_url:
        logger.error("无法获取Ollama URL配置")
        return
    
    # 检查Ollama连接
    logger.info("=== Ollama连接检查 ===")
    connection_result = await check_ollama_connection(ollama_url)
    
    if connection_result.get('connected'):
        logger.info("✅ Ollama连接成功")
        logger.info(f"可用模型: {connection_result.get('models', [])}")
        logger.info(f"模型数量: {connection_result.get('model_count', 0)}")
        
        # 检查目标模型是否可用
        available_models = connection_result.get('models', [])
        if model_name in available_models:
            logger.info(f"✅ 目标模型 '{model_name}' 可用")
            
            # 测试并发请求
            logger.info("=== 并发请求测试 ===")
            concurrent_result = await test_concurrent_requests(ollama_url, model_name, 3)
            
            if 'error' not in concurrent_result:
                logger.info(f"✅ 并发测试完成")
                logger.info(f"总耗时: {concurrent_result['total_time']:.2f}秒")
                logger.info(f"成功请求: {concurrent_result['successful_requests']}/{concurrent_result['total_requests']}")
                logger.info(f"平均响应时间: {concurrent_result['average_duration']:.2f}秒")
                
                # 分析并发效果
                if concurrent_result['successful_requests'] > 1:
                    avg_duration = concurrent_result['average_duration']
                    total_time = concurrent_result['total_time']
                    if total_time < avg_duration * concurrent_result['successful_requests']:
                        logger.info("✅ 检测到并发处理效果")
                    else:
                        logger.warning("⚠️  可能存在并发限制")
            else:
                logger.error(f"❌ 并发测试失败: {concurrent_result['error']}")
        else:
            logger.warning(f"⚠️  目标模型 '{model_name}' 不在可用模型列表中")
            logger.info(f"可用模型: {available_models}")
    else:
        logger.error(f"❌ Ollama连接失败: {connection_result.get('error', 'Unknown error')}")
        logger.info("请检查:")
        logger.info("1. Ollama服务是否启动")
        logger.info("2. URL配置是否正确")
        logger.info("3. 网络连接是否正常")
    
    # 输出配置建议
    logger.info("=== 配置建议 ===")
    max_concurrent = CURRENT_CONFIG.get('max_concurrent_requests', 8)
    logger.info(f"当前最大并发数: {max_concurrent}")
    
    if connection_result.get('connected'):
        logger.info("建议:")
        logger.info("1. 根据测试结果调整并发参数")
        logger.info("2. 监控系统资源使用情况")
        logger.info("3. 定期检查Ollama服务状态")
    else:
        logger.info("建议:")
        logger.info("1. 确保Ollama服务正常运行")
        logger.info("2. 检查防火墙和网络配置")
        logger.info("3. 验证模型是否正确加载")
    
    logger.info("检查完成")


if __name__ == "__main__":
    asyncio.run(main())
